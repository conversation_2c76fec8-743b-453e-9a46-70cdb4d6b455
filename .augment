# Augment Rules for neatlogic-web

## Project Overview
This is a Vue.js 2.7.16 frontend project for the neatlogic platform, using iView 4.x UI framework with custom modifications. The project uses Vue CLI 5.x and webpack 5.x for building.

## Code Style and Standards

### Vue Component Structure
- Use PascalCase for component names (enforced by ESLint)
- Follow the standard Vue component lifecycle order: beforeCreate, created, beforeMount, mounted, beforeUpdate, updated, activated, deactivated, beforeDestroy, destroyed
- Use async imports for components: `() => import('@/path/to/component')`
- Always include the `name` property in component definitions

### ESLint Configuration
- Use single quotes for strings (enforced)
- 2-space indentation (enforced)
- Semicolons required (enforced)
- No trailing commas (enforced)
- camelCase naming disabled for properties to accommodate API responses
- Vue-specific rules: multi-word component names disabled, prop mutation disabled

### File Organization
```
src/
├── commercial-module/     # Commercial modules
├── community-module/      # Community modules  
├── resources/            # Shared resources
│   ├── api/             # API definitions
│   ├── assets/          # Static assets (CSS, JS, images)
│   ├── components/      # Reusable components
│   ├── directives/      # Vue directives
│   ├── filters/         # Vue filters
│   ├── mixins/          # Vue mixins
│   ├── plugins/         # Custom plugins (TsForm, TsDialog, etc.)
│   └── store/           # Vuex store
└── views/               # Page components
    ├── components/      # Page-specific components
    └── pages/           # Route pages
```

### Naming Conventions
- Components: PascalCase (e.g., `TsContain`, `TsForm`)
- Files: kebab-case for Vue files (e.g., `component-library.vue`)
- API files: kebab-case (e.g., `application-config.js`)
- Variables: camelCase
- Constants: UPPER_SNAKE_CASE

### Component Development Guidelines

#### Custom Components Prefix
- Use `Ts` prefix for custom UI components (e.g., `TsContain`, `TsForm`, `TsCard`)
- Components are located in `src/resources/components/` or `src/resources/plugins/`

#### Props Definition
- Always define prop types
- Use validator functions for enum-like props
- Provide default values where appropriate
- Use descriptive prop names

#### Template Structure
- Use semantic HTML elements
- Prefer v-if over v-show for conditional rendering
- Use scoped slots for flexible content injection
- Follow the slot naming pattern: `topLeft`, `topCenter`, `topRight`, `content`, `sider`, etc.

#### Script Structure
```javascript
export default {
  name: 'ComponentName',
  components: {
    // Async component imports
  },
  props: {
    // Prop definitions with types and defaults
  },
  data() {
    return {
      // Component state
    };
  },
  computed: {
    // Computed properties
  },
  watch: {
    // Watchers
  },
  // Lifecycle hooks in order
  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  methods: {
    // Component methods
  }
};
```

### API Integration
- API files are organized by module in `src/resources/api/`
- Use axios for HTTP requests
- Import axios from `../../http` (relative to API file location)
- API methods should return the axios promise directly
- Use descriptive method names that indicate the action

### Internationalization (i18n)
- Use `this.$t()` for all user-facing text
- Language files are in `src/resources/assets/languages/`
- Organize translations by category: dialog, page, term, message, form

### Styling Guidelines
- Use Less for styling with global variables defined in build config
- Global variables: `top-height: 50px`, `actionbar-height: 50px`, `space-normal: 16px`, etc.
- Use scoped styles in components
- Follow BEM-like naming for CSS classes
- Use utility classes: `bg-grey`, `text-action`, `pl-nm`, `pr-nm`, etc.

### Build and Development
- Use `cnpm` instead of `npm` for package management (recommended in docs)
- Development server: `cnpm run serve`
- Production build: `cnpm run build`
- Linting: `cnpm run lint` (auto-fix enabled)
- Use Node.js v18.x for stability

### Module System
- Support for both commercial and community modules
- Modules can have custom configurations in `customconfig.js`
- Dynamic module loading based on activated commercial modules
- Use alias imports: `@/` for src, `base-module` for resources, etc.

### Performance Considerations
- Use async component loading for better code splitting
- Implement virtual scrolling for large lists
- Use `v-show` vs `v-if` appropriately
- Optimize bundle size with proper imports

### Testing
- Unit tests are in `tests/unit/`
- Use Vue Test Utils for component testing
- Follow the existing test structure and patterns

### Documentation
- Include `.md` files for complex components (see `TsContain.md`, `TsCard.md`)
- Document component props, slots, and methods
- Provide usage examples in documentation

## Dependencies Management
- Always use package managers (cnpm/npm) instead of manually editing package.json
- Use specific versions to avoid compatibility issues
- Test with Node.js v18.x and cnpm v8.2.0 for stability

## Security and Best Practices
- Use `v-dompurify-html` for rendering HTML content safely
- Validate all user inputs
- Use proper authentication checks (`hasAuthorization`)
- Implement proper error handling and user feedback
- Use HTTPS for API calls in production

## State Management
- Use Vuex for global state management
- Keep component state local when possible
- Use provide/inject for deeply nested component communication
- Implement proper state normalization for complex data

## Error Handling
- Implement global error handling for API calls
- Provide user-friendly error messages
- Use try-catch blocks for async operations
- Log errors appropriately for debugging

## Accessibility
- Use semantic HTML elements
- Provide proper ARIA labels where needed
- Ensure keyboard navigation support
- Maintain proper color contrast ratios
- Support screen readers

## Browser Compatibility
- Support modern browsers (ES6+)
- Use polyfills for required features
- Test across different browsers and devices
- Consider mobile responsiveness

## Code Review Guidelines
- Follow the established patterns in the codebase
- Ensure proper error handling
- Verify internationalization implementation
- Check for performance implications
- Validate accessibility requirements
- Ensure proper documentation

## Git Workflow
- Use descriptive commit messages
- Follow conventional commit format when possible
- Create feature branches for new development
- Ensure code passes linting before committing
- Include relevant documentation updates

## Environment Configuration
- Use `apiconfig.json` for environment-specific settings
- Configure tenant name and URL prefix appropriately
- Use environment variables for sensitive data
- Maintain separate configs for development and production
