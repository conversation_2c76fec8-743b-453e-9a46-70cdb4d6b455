@import (reference) '~@/resources/assets/css/variable.less';
.theme(@bg-grey, @bg-grey-hover, @success-bg-color, @error-bg-color, @bg-op, @primary-grey, @border) {
  .file-li {
    border: 1px solid;
    .file-title {
      border-bottom: 1px solid;
      padding: 4px 10px 4px 4px;
      .btn-toggle {
        width: 24px;
        text-align: center;
      }
      .file-name {
        &:before {
          margin-right: 4px;
        }
      }
    }
    .file-main {
      pre {
        margin: 0;
      }
      table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
        td {
          font-size: 12px;
          padding: 2px 8px;
        }
        td.code-lineno {
          border-right: 1px solid var(--border, @border);
          background: var(--bg-op, @bg-op);
          white-space: nowrap;
          word-break: keep-all;
          text-align: center;
          font-size: 12px;
          width: 34px;
          user-select: none;
          padding: 2px 0;
        }
        td.code-content {
          background: var(--bg-op, @bg-op);
          .hljs {
            background: var(--bg-op, @bg-op);
            padding: 0 2px;
          }
        }

        tr {
          &:not(.linetype-to) {
            &:not(.linetype-from) {
              &:hover {
                background: var(--bg-grey, @bg-grey);
                td {
                  background: var(--bg-grey, @bg-grey);
                }
                .hljs {
                  background: var(--bg-grey, @bg-grey);
                }
              }
            }
          }
          .code-content {
            padding-left: 14px;
            white-space: pre-wrap;
          }
          &.linetype-from {
            opacity: 0.8;
            td:not(.to) {
              background: var(--error-bg-color, @error-bg-color); // 代码删除后高亮颜色
              .hljs {
                background: var(--error-bg-color, @error-bg-color); // 代码删除后高亮颜色
              }
            }
            &:hover {
              opacity: 1;
              td.to {
                background: var(--error-bg-color, @error-bg-color); // 代码删除后高亮颜色
                .hljs {
                  background: var(--error-bg-color, @error-bg-color); // 代码删除后高亮颜色
                }
              }
            }
            .code-content:not(.to) {
              position: relative;
              &:before {
                content: '-';
                position: absolute;
                top: 50%;
                left: 0;
                width: 14px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                margin-top: -10px;
              }
            }
          }
          &.linetype-to {
            opacity: 0.8;
            td:not(.from) {
              background: var(--success-bg-color, @success-bg-color);
              .hljs {
                background: var(--success-bg-color, @success-bg-color);
              }
            }
            &:hover {
              opacity: 1;
              td.from {
                background: var(--success-bg-color, @success-bg-color);
                .hljs {
                  background: var(--success-bg-color, @success-bg-color);
                }
              }
            }
            .code-content:not(.from) {
              position: relative;
              &:before {
                content: '+';
                position: absolute;
                top: 50%;
                left: 0;
                width: 14px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                margin-top: -10px;
              }
            }
          }
        }
      }
    }
    &.active {
      table {
        td.code-content {
          background: var(--primary-grey, @primary-grey);
          .hljs {
            background: var(--primary-grey, @primary-grey);
          }
        }
      }
    }
    &.separate {
      .to-lineno {
        border-left: 1px solid var(--border, @border);
      }
    }
  }
}
html {
  .theme(@default-bg-grey, @default-bg-grey-hover, @default-success-bg-color, @default-error-bg-color, @default-op, @default-primary-grey, @default-border);
  &.theme-dark {
    .theme(@dark-bg-grey, @dark-bg-grey-hover, @dark-success-bg-color, @dark-error-bg-color, @dark-op, @dark-primary-grey, @dark-border);
  }
}