@import (reference) '~@/resources/assets/css/variable.less';
.theme(@success-color, @success-bg-color, @warning-bg-color,@error-color,@error-bg-color,@text){
  .compare {
    padding-bottom: 16px;
    ::v-deep .bg-insert{
      background:  @success-bg-color;
      color: @text;
    }
    ::v-deep .bg-delete{
      background: @error-bg-color;
      color: @text;
    }
    ::v-deep .bg-update{
      background: @warning-bg-color;
      color: @text;
    }
    ::v-deep .bg-fillblank{
      opacity: 0;
    }
    ::v-deep .insert {
      color: @success-color;
    }
    ::v-deep .delete {
      color: @error-color;
    
    }
    ::v-deep .update {
      color: @warning-color;
    }
    ::v-deep .fillblank {
      opacity: 0;
      pointer-events: none;
    }
    ::v-deep tr.insert{
      background:  @success-bg-color;
    }
    ::v-deep tr.update{
      background:  @warning-bg-color;
    }
    ::v-deep tr.delete{
      background:  @error-bg-color;
    }
    ::v-deep .nav-insert {
      background:  @success-bg-color;
    }
    ::v-deep .nav-change {
    background:  @warning-bg-color;
    }
    ::v-deep .nav-update {
      background:  @warning-bg-color;
    }
    ::v-deep .nav-delete {
      background:  @error-bg-color;
    }
    .table-insert {
      background:  @success-bg-color;
    }
    .table-update {
      background:  @warning-bg-color;
    }
    .table-delete {
      background:  @error-bg-color;
    }
    .table-fillblank {
      opacity: 0;
    }
  }
}
html {
  .theme(@default-success-color, @default-success-bg-color, @default-warning-bg-color,@default-error-color,@default-error-bg-color,@default-text);

  &.theme-dark {
    .theme(@dark-success-color, @dark-success-bg-color, @dark-warning-bg-color,@dark-error-color,@dark-error-bg-color,@dark-text);
  }
}
