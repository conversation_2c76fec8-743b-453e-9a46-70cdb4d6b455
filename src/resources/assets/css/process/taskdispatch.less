@import (reference) '~@/resources/assets/css/variable.less';

.workorderReport {
  position: relative;
  background: @background-color;

  .dispath-main {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .workorder-task {
    position: relative;

    .task-list {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;

      .search-main {
        padding: 0 32px 0 16px;

        .title {
          padding-bottom: @space-sm;
        }
      }

      .task-div {
        position: relative;
        margin-top: 8px;
        height: calc(100vh - 38px - 32px - 120px);

        ul {
          height: 100%;
          overflow-y: auto;
          padding-right: 16px;
        }

        li {
          width: 100%;
          height: 36px;
          line-height: 36px;
          margin-bottom: 4px;
          padding: 0 16px;
          cursor: pointer;
        }
      }
    }
  }

  .workorder-main {
    position: relative;
    // height: calc(100vh - 56px - 48px - 48px);
    height: 100%;
    overflow: auto;

    .form-main {
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      position: relative;

      .from-list {
        padding-bottom: 16px;

        .title {
          width: 100%;
          padding-bottom: 10px;
        }

        .input-main {
          padding: 16px;

          .user_detail {
            color: @primary-color;
            font-size: @font-size-small;
          }

          .userContent {
            text-align: left;

            .item {
              >span {
                padding-right: 5px;
              }
            }
          }

          .ivu-poptip-title-inner {
            text-align: left;
          }
        }

        button {
          margin-right: 8px;
        }

        .editor {
          padding-bottom: 10px;
        }
      }
    }
  }
}

.sitemapMain {
  width: 100%;
  min-height: 479px;
  height: 100%;
}

.fullModel {
  position: absolute;
  right: 50px;
  top: 8px;
  padding: 0;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 12px;
  }
}

.temporaryModelBox {
  position: relative;
  width: 100%;
  padding: 32px 0 0 64px;
  text-align: left;

  .icon-problemlist {
    position: absolute;
    left: 16px;
    top: 26px;
    font-size: 26px;
    color: @warning-color;
  }

  p {
    line-height: 22px;
  }
}

.submitModelBox {
  text-align: center;

  .submit-model-title {
    font-size: @line-height-menu;
    margin-bottom: 20px;
  }

  .submit-btn-list {
    margin-bottom: 10px;
  }
}

.taskDetail {
  //工单处理
  position: relative;
  .tasknodedetail {
    .ivu-layout.ivu-layout-has-sider {
      height: 100%;
    }
  
    ::v-deep .tscontain-body {
      overflow-y: hidden;
    }
  }
  .workorder-task {
    position: relative;
    width: 100%;
    height: 100%;

    .task-list {
      width: 100%;
      height: 100%;
      overflow-y: auto;

      .title-top {
        width: 100%;
        padding-bottom: @space-md;
      }

      .task-secrch {
        position: relative;
        width: 100%;
        padding-right: 16px;
        padding-bottom: 10px;
      }

      .task-div {
        position: relative;
        height: calc(100vh - 100px - 32px - 42px - 16px);

        ul {
          height: 100%;
          overflow-y: auto;
          padding: 0 @space-md @space-md 0;
        }

        .task-li {
          position: relative;
          width: 100%;
          padding: 8px 10px;
          margin-bottom: 8px;
          cursor: pointer;

          .tast-time {
            position: absolute;
            right: 8px;
            top: 8px;
          }

          .task-title-box {
            padding-top: 8px;

            .number {
              font-size: 12px;
              height: 14px;
            }

            .text {
              font-size: 16px;
            }
          }

          .task-step-title {
            padding-top: 8px;
            font-weight: bold;
          }
        }
      }
    }
  }

  .taskdetail-top {
    position: relative;
    display: flex;
    justify-content: space-between;

    .title-box {
      display: inline-flex;
      align-items: center;
      margin-left: 0;

      .type-tip {
        margin-right: 6px;
        display: inline-block;
        max-width: 90px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        border: 1px solid;
        border-radius: 11px;
        font-size: 13px;
        padding: 0 4px;
      }

      .task-title {
        display: inline-flex;
        flex-direction: column;

        &-id {
          display: inline-block;
          height: 12px;
          line-height: 12px;
          font-size: 12px;
          text-transform: uppercase;
          vertical-align: middle;
          cursor: copy;

          .tsfont-copy {
            cursor: pointer;
            margin-left: 3px;
          }
        }

        &-dividing {
          margin: 0 6px;
          width: 0;
          height: 12px;
          border-right: 1px solid;
          vertical-align: middle;
        }

        &-name {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          font-size: 16px;

          .title-text {
            color: inherit;
            height: 22px;
            line-height: 22px;
          }
        }
      }
    }

    .edit-title {
      display: inline-block;
    }

    .title-text {
      margin-right: 10px;
    }

    .toolbar-left {
      display: inline-block;
    }

    .toolbar-right {
      position: absolute;
      right: 0;
      top: 0;

      .ivu-icon {
        vertical-align: bottom;
      }

      .btn-margin-left {
        margin-left: @space-xs;
      }

      .action-group {
        display: inline-block;
      }
    }
  }

  .valid-main {
    position: absolute;
    top: 38px;
    right: 8px;
    z-index: 100;
    width: 320px;
    max-height: 300px;
    overflow-y: auto;
  }
}

// 提示框
.tooltip-box {
  position: absolute;
  top: 0;
  left: 0;

  .tooltip {
    position: absolute;
    top: 100%;
    left: 20px;
    display: block;
    opacity: 1;
    width: 100%;
    transform: translateY(10px);
    transition: all 0.25s ease-out;
    width: 250px;
    max-height: 300px;
    border-radius: 2px;
    padding: 0 16px;

    &.tipright {

      //右边
      &:before {
        position: absolute;
        display: inline-block;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        left: -7px;
        top: 10px;
        content: '';
      }

      &:after {
        position: absolute;
        display: inline-block;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        left: -6px;
        top: 11px;
        content: '';
      }
    }

    &.tipleft {

      /**左边**/
      &:before {
        position: absolute;
        display: inline-block;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        right: -7px;
        top: 10px;
        content: '';
      }

      &:after {
        position: absolute;
        display: inline-block;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        right: -6px;
        top: 11px;
        content: '';
      }
    }

    /**上边**/
    &.tiptop {
      &:before {
        position: absolute;
        display: inline-block;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        left: 10px;
        bottom: -7px;
        content: '';
      }

      &:after {
        position: absolute;
        display: inline-block;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        left: 11px;
        bottom: -6px;
        content: '';
      }
    }

    /**下边**/
    &.tipbottom {
      &:before {
        position: absolute;
        display: inline-block;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        left: 10px;
        top: -7px;
        content: '';
      }

      &:after {
        position: absolute;
        display: inline-block;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        left: 11px;
        top: -6px;
        content: '';
      }
    }

    .tooltip-title {
      line-height: 40px;
      border-bottom: 1px solid;
    }

    .tooltip-content {
      line-height: 32px;
      width: 100%;
      overflow: auto;
      max-height: 260px;

      .tooltip-list {
        position: relative;
        padding-left: 80px;

        .label {
          width: 80px;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}