{"expression": {"eq": "等于", "ne": "不等于", "like": "包含", "notlike": "不包含", "empty": "为空", "notempty": "不为空", "between": "在区间内"}, "cmdb": {"cicount": "模型数量", "isshowintopo": "是否在拓扑显示", "showintopo": "拓扑中显示", "keylevel": "关键层级", "levelnumber": "{level}层", "showrel": "展示关系", "extendlevel": "展开层数", "affectcientity": "影响配置项", "selectcientity": "选择配置项", "selectedcientity": "选中配置项", "attributelist": "属性列表", "relpathci": "请选择路径必须包含的模型", "relpathcount": "共 {count} 个关系路径", "relpathcounting": "正在计算关系路径...", "belongci": "所属模型", "customviewconfig": "自定义视图配置", "relpathlist": "关系路径列表", "fieldname": "字段名称", "fieldtype": "字段类型", "displaytype": "显示方式", "relationgroup": "关系分组", "legalcheckconfig": "合规检查设置", "newrule": "新建规则", "rulegroup": "规则组合", "chooseci": "选择模型", "citype": "模型类型", "onlyabstractci": "只看抽象模型", "onlyvirtualci": "只看虚拟模型", "parentci": "父模型", "cifile": "模型文件", "cimanage": "管理模型", "addci": "添加模型", "editcientity": "编辑配置项", "deletecientity": "删除配置项", "fromcientity": "来源配置项", "recovercientity": "恢复配置项", "cascaderelation": "级联关系", "transactionmanage": "管理事务", "viewpassword": "查看密码字段", "calccascaderelation": "计算级联关系", "invokecount": "关联个数", "upsideuniquekey": "上游端唯一标识", "downsideuniquekey": "下游端唯一标识", "cientity": "配置项", "targetci": "目标模型", "upsidename": "上游端名称", "downsidename": "下游端名称", "cientityname": "配置项名称", "nonamecientity": "无名配置项", "newcientity": "新的配置项", "uniquerule": "唯一规则", "manualinput": "人工录入", "upside": "上游端", "downside": "下游端", "ci": "配置项模型", "transactionid": "事务id", "virtualci": "虚拟模型", "abstractci": "抽象模型", "configfile": "配置文件", "activedate": "有效日期", "keymodel": "关键模型", "view": "视图", "legalcheck": "合规检查", "viewcientity": "查看配置项", "hidetopo": "隐藏拓扑", "showtopo": "显示拓扑", "viewname": "视图名称", "alertlevel": "告警级别", "customview": "自定义视图", "basicattribute": "基本属性", "allattribute": "所有属性", "nameattribute": "名称属性", "defaultattribute": "默认属性", "group": "团体", "history": "历史记录", "uncommittransaction": "未提交事务", "radius": "圆角", "transaction": "事务", "importci": "导入模型", "exportci": "导出模型", "extendto": "继承自", "editci": "编辑模型", "cientitylist": "配置项列表", "needexportcientity": "请选择需要导出的属性", "invokelist": "引用视图列表", "topo": {"dot": "分层布局", "circo": "环形布局", "neato": "张力布局", "osage": "阵列布局", "fdp": "无向布局", "twopi": "星形布局"}, "fulljoin": "全连接", "leftjoin": "左连接", "rightjoin": "右连接", "setstartmodel": "设为起始模型", "innerproperty": "内部属性", "taginvoked": "当前标签已被引用", "viewsetting": "视图配置", "rebuildallview": "重建所有视图", "resourcetarget": "资源对象", "sceneview": "场景视图", "collectiondata": "集合数据", "discoveryrule": "发现规则", "unknowndevice": "未知设备", "targetoid": "目标oid", "matchrule": "匹配规则", "objectcategory": "对象大类", "objecttype": "对象分类", "model": "型号", "objectname": "对象名", "cientityattribute": "配置项属性", "dataview": "数据视图", "topoview": "拓扑视图", "transactionauditperiod": "事务保存期限", "transactiongroupid": "事务组id", "invokeasset": "关联资产", "exceptiontemplate": "异常模板", "componentlist": "内部组件列表", "innerattributelist": "内部属性列表", "datasaving": "数据保存中...", "appnamemodulename": "应用名称、模块名称", "exportselectedassets": "导出选中资产", "exportallfiterconditionassets": "导出满足当前过滤条件的所有资产", "publicprivateaccountchooseoneaccountdesc": "公共账号和私有账号的集合不能为空，请至少填写一个账号", "successfullyboundaccountforassetstarget": "{target}条资产绑定账号成功", "failedtobindaccountforassetstarget": "{target}条资产绑定账号失败", "onlyexportselected": "仅导出选中数据", "driverci": "驱动模型", "addcientity": "新增配置项", "resourcetypetreenosettingdesc": "资产清单数据来源于scence_ipobject_detail视图，在视图设置中该视图未设置成功，资产清单功能不可用", "gotoresourceentitymanagepage": "点击此处设置该视图", "resourcetypetreesettingdesc": "请选择一个模型作为资产清单分类的根目录", "mainci": "主模型", "inherentfield": "固有字段", "attrci": "字段所在模型", "inspectime": "巡检时间", "monitortime": "监控时间", "modelkey": "模型标识", "modelname": "模型名称", "toattr": "映射属性", "fromci": "上游模型", "toci": "下游模型", "privatedataview": "个人数据视图", "publicsceneview": "全局拓扑视图", "publicdataview": "全局数据视图", "changememo": "变更说明", "globalattr": "全局属性", "pleaseselectlayout": "请选择布局", "gotoresourcetypetreesetting": "请到配置管理-资产清单中设置根目录", "resourcebatchsearchtooltip": "批量搜索是关键字模糊搜索，其中批量搜索ip时，支持通配符*，匹配任意个字符，例如192.168.*.1可匹配***********、************、*************", "onlybackbone": "只显示主干关系", "showallrel": "显示所有分支关系", "modemapping": "模型映射", "ciconfignumber": "配置项数量", "relaction": "关系策略", "attrmapping": "属性映射", "addonedata": "添加一条数据", "addmoredata": "添加多条数据", "addonerel": "添加单个关系", "addmorerel": "添加多个关系", "rerunsteptosync": "回退重新同步", "rerunsteptosynctip": "流程回退后，再次流转至当前步骤时，重新同步", "onlyupward": "只看上游", "onlydownward": "只看下游", "noextend": "隐藏继承关系", "nochange": "没有任何修改", "cilevel": "模型层级", "cidirectory": "模型目录", "directoryname": "目录名称", "noaddcidirectory": "未添加模型目录，", "currentcatalogcitenodeletedescrition": "当前目录节点已被引用，请解除引用后删除", "autoexec": "自动化操作", "grouptypedescreadonly": "只读：查看配置项，可作为自动化中，【查询类】组合工具的执行目标。", "grouptypedescmaintain": "维护：在只读权限的基础上，增加对配置项进行修改。", "grouptypedescautoexec": "自动化操作：在只读权限基础上，增加可作为自动化中，【操作类】组合工具的执行目标", "abstractciallowextend": "被继承模型只能是抽象模型", "asyncpolicy": "同步策略", "globaleditmodetip": "全局模式，不给属性代表删除", "partialeditmodetip": "局部模式，不给属性则不改", "treedraginfo": "拖动模型可以改变模型的顺序和从属关系，处于搜索状态时不可拖动", "startci": "先选择起始模型", "writeci": "写入模型", "writecitip": "选择常量或通过表单映射的方式，指定数据写入的模型。若父模型为抽象模型，不支持添加配置项，需将数据写入子模型。", "subci": "子模型", "selectrelpath": "选择关系路径", "relciconfigtip": "配置父模型后，不可配置子模型；配置子模型后，不可配置父模型，但可配置其他子模型", "topotemplate": "拓扑模板", "selectagain": "重新选择", "topotype": "展开方式", "tooltipscope": "仅在配置项列表、编辑和详情页面生效", "dataconversionsetting": "转换配置", "appendrel": "追加关系", "replacerel": "替换关系", "relationaltable": "关联表格", "relationalcondition": "关联条件", "relationalpretable": "关联上层表格", "searchable": "允许搜索", "pushmqhelp": "配置项发生变化时发布此属性到MQ", "searchablehelp": "允许搜索的属性可以在高级搜索中作为过滤条件，也能在自定义数据视图中连接其他模型的可搜索属性", "topicci": "订阅模型", "condition": "综合条件", "automatch": "自动匹配", "cancelautomatch": "撤销自动匹配", "collection": "集合", "parentattr": "父属性", "initiativecollect": "主动采集", "passivitycollect": "被动采集", "selectedattr": "可选属性", "scripthelp": "转换脚本语法仅支持ES5语法，点击可选属性可复制获取属性值的表达式，把新值赋值给变量$value即可完成转换", "expression": "表达式", "transformscript": "转换脚本", "relmapping": "关系映射", "globalattrmapping": "全局属性映射", "matchfield": "匹配字段", "supportmultimodelfile": "帮助：支持同时导入多个模型文件", "invokeattr": "引用属性", "ciid": "模型id", "cientityid": "配置项id", "cientityicon": "配置项图标", "editablefield": "可编辑属性", "deletecitip": "当前配置项已被删除", "expandbyscene": "按场景展开", "expandbylevel": "按层数展开", "deleteattr": "删除属性", "deleteglobalattr": "删除全局属性", "groupbyci": "按模型分组", "showrellabel": "显示关系类型", "exportformodel": "导出为Model", "exportforexcel": "导出为Excel", "isterm": "是否关键字", "batchimport": "批量上传", "errordetail": "异常详情", "collect": "采集", "pause": "停止", "cientityisnotfound": "配置项不存在", "testaccount": "测试结果", "repeataccount": "协议相同且用户名相同，同一资产不可绑定多个协议相同且用户名相同的账号。", "datadetail": "数据明细"}, "process": {"workordercenter": "工单中心", "catalog": "服务", "catalogmanage": "服务目录", "cataloglist": "目录列表", "task": "工单", "flow": "流程", "formlist": "表单列表", "relprocess": "关联流程", "scoringdimension": "评分维度", "subtaskpolicy": "子任务策略", "policy": "策略", "submittask": "上报工单", "viewflowchart": "查看流程图", "relcatalog": "关联服务", "catalogname": "服务名称", "report": "上报", "reportcontent": "上报内容", "reportcatalog": "上报服务", "usernumber": "工号", "focususer": "关注人", "planStartEndTime": "计划起止时间", "changeowner": "变更经理", "changecontent": "变更说明", "changeparams": "变更变量", "startTimeWindow": "时间窗口", "changedetail": "变更详情", "unlimitednum": "不限人数", "anypersoncompletes": "任意一人完成", "allpersoncompletes": "所有人完成", "dealwithusernum": "处理人数", "completepolicy": "完成策略", "dealwithusernumdesc": "处理人数为此策略下，新增子任务需选择的处理人数", "btntext": "按钮文案", "custombtn": "自定义按钮", "solution": "解决方案", "solutioncontent": "解决方案内容", "relclassify": "关联分类", "relclassifydes": "选中父级分类时，自动向下穿透，即父类下的所有子类都将自动关联该解决方案。", "subclassify": "下级分类", "relsolution": "关联解决方案", "eventtype": "事件类型", "noderules": "节点规则", "resetflowtip": "重置后，将恢复到最近一次保存时的配置，是否放弃当前修改？", "workflow": "工作流", "showpriority": "显示优先级", "defaultpriority": "默认优先级", "serwindow": "服务时间", "sertype": "服务类型", "reporauth": "上报授权", "tranferreport": "转报", "allowtranferreport": "允许转报", "transfersettings": "转报设置", "transfersettingstip": "转报可选的关系类型由上方服务类型决定", "useoriginalreporter": "使用原上报人", "serallowforwarding": "允许转报的服务", "authorizeduser": "授权对象", "worknumper": "工单号前缀", "worknumrules": "工单号规则", "updateworknum": "更新工单号", "nodename": "节点名称", "hideparams": "隐藏已匹配参数", "showparams": "显示已匹配参数", "btnmapping": "按钮映射", "statusmapping": "状态映射", "othersmapping": "其它映射", "changetemp": "变更模板", "soptemp": "SOP模板", "changegrouptip": "支持关联用户组，创建变更模板时可通过该分组快速过滤变更模板", "changetypetip": "支持自定义添加类型，创建变更时可通过该类型快速过滤变更模板", "templatesteps": "模板步骤", "soptip": "请从右侧拖拽SOP模板到此处", "tempnotreferenced": "模板未激活，不可引用", "tempdelete": "模板被引用不可删除", "mappingmethod": "映射方式", "paramstip": "提示：修改变量后，将同步替换所有变更步骤中引用的对应变量", "paramsdeltip": "删除后无法恢复，并会同步清空以下步骤中引用的当前变量。", "stepname": "步骤名称", "step": "步骤", "stepuser": "步骤处理人", "stepinfor": "步骤信息", "stepstatus": "步骤状态", "batchedituser": "批量编辑处理人", "planstartdate": "计划开始日期", "dealwithuser": "处理人", "paramlist": "变量列表", "encoded": "编码", "changestep": "变更步骤", "currentstep": "当前步骤", "archivedtype": "归档类型", "actualstarttime": "实际开始", "actualendtime": "实际结束", "mycreated": "我创建的", "savedraftflow": "保存草稿范围：最近 6 次", "autosaveinterval": "自动保存间隔：30s", "restoredrafts": "从草稿中恢复文件", "autosavedatetime": "自动保存日期/时间", "importjob": "导入工单", "downloadtemp": "下载模板", "importjobtip": "为避免导入失败，请使用本页面下载的模板", "importjobrule": "支持导入10MB以内的.xlsx文件", "reportstatus": "上报状态", "recordid": "记录ID", "servicechannel": "服务通道", "importtime": "导入时间", "pleasewait": "工单流转中，请等待...", "relatedknowledge": "关联知识", "sourcename": "上报渠道", "proxy": "代报人", "expiretime": "超时时间点", "deadline": "截止", "transferto": "流转至", "nottransfer": "不流转", "alwaystransfer": "直接流转", "act": "代", "pendinguser": "待处理人", "changestatus": "变更状态", "timeout": "超时时间", "repeatedevent": "重复事件", "steplog": "步骤日志", "reltask": "关联工单", "subtask": "子任务", "formpriorityrule": "表单联动规则匹配到多个优先级，请修改表单或联系管理员", "autostart": "自动开始", "autostarttip": "仅处理人唯一时有效", "timedout": "已超时", "distancetimeout": "距离超时", "notfocustask": "取消关注", "focustask": "关注工单", "mytodo": "我的待办", "pleaseinputtaskcontent": "标题、工单号或上报内容关键字", "formfilter": "表单过滤", "attrdragtip": "已选属性（拖动修改排序）", "workordercentertypedes": "作为左侧菜单显示时的分类名称，不设置则归类到默认分类：工单中心", "workordertypenumdes": "左侧工单分类是否显示总数,默认为否", "converttoknowdoc": "转为知识文档", "pleselectprioritystep": "选择优先要处理的步骤", "selectaskuser": "指定{target}处理人", "changesuspension": "变更暂停", "circulation": "流转", "linename": "线名称", "successcirculation": "确认成功并流转", "failedcirculation": "确认失败并流转", "callrecord": "调用记录", "callbackrecord": "回调记录", "autoexeccompleteerror": "自动化作业正在执行，无法人工流转", "assigntitle": "指定步骤处理人", "targettime": "期望完成时间", "processsteplist": "步骤列表", "changeinfor": "变更信息", "callbackstrategy": "回调策略", "pollinginterval": "轮询间隔", "externalcall": "外部调用", "ccompleteerror": "无法人工流转", "accessorieslist": "附件清单", "createsubtask": "创建子任务", "copyreport": "复制上报", "recoverstep": "恢复工单步骤", "redo": "评分之前回退", "markrepeat": "标记重复事件", "createtask": "创建任务（新的子任务）", "taskstatus": "任务状态", "replycanclicktip": "回复框不为空时允许点击", "pleusernum": "请选择{target}位用户", "workordersuggestion": "您对本工单的建议...", "selecteditemsnum": "已选择<span class='text-primary'>{target}</span>项", "selectrelationdialogtip": "请确认您要找的工单是否符合当前关系类型？ 如需增加,请联系管理员！", "relationtip": "当前工单所在服务未配置任何关系类型，无法手动关联其他工单，如需添加，请联系管理员!", "transfer": "转", "tasktitle": "工单标题", "disassociate": "解除关联", "unbind": "解除绑定", "repeatedeventtip": "被选中的工单中，包含已被标记为重复事件的工单。以下工单将被标记为同一重复事件。", "canotunbindtip": "转报自动关联无法解除", "solutiontip": "更换解决方案后，会自动覆盖富文本框中的所有内容", "savesolution": "保存解决方案", "tranknowledgetip": "请勾选需要保存的内容，支持拖动排序", "tasksaveknow": "工单保存为知识", "nodestatus": "节点状态", "currentnodewillflowtime": "当前节点将于{time}流转，", "distanceflow": "距离流转", "originaltask": "原工单", "effectivenesstimesetting": "时效设置", "flowsetting": "流程设置", "scoresetting": "评分设置", "nodesetting": "节点设置", "linksetting": "线条设置", "nodevalidpassed": "节点验证通过", "poliyuser": "分配处理人", "erroruser": "异常处理人", "prestepassignvalid": "分配处理人：由前置步骤处理人指定必填", "copyworkerpolicyvalid": "分配处理人：复制前置步骤处理人必填", "formworkerpolicyvalid": "分配处理人：表单值必填", "assignworkerpolicyvalid": "分配处理人：自定义必填", "assignconfigvalid": "分配处理人：分派器必填", "noderultvalid": "节点不符合规则", "notselectchangelistvalid": "不允许存在未关联的变更创建", "changeexistinpairsvalid": "变更创建与变更处理节点必须成对存在", "changeonlyonerelvalid": "变更创建节点只允许被一个变更处理节点关联", "selectchangevalid": "必须选择关联的变更创建", "selectformitemvalid": "至少选择一个关联表单组件", "paramsMapping": "参数映射", "circulationtime": "流转时间", "circulationtimetip": "请选择表单中设置为必填的日期组件", "formupdateselecttimevalid": "表单组件已被修改，请重新选择流转时间", "associatedsteps": "关联步骤", "transferuser": "转交对象", "relpriority": "关联优先级", "duration": "时长", "circulationpath": "流转路径", "slatip": "时效标签", "priorityaging": "优先级时效", "singleexecution": "单次执行", "periodicexecution": "周期执行", "handoverstrategy": "转交策略", "timeoutpolicy": "超时策略", "limitationconditions": "时效条件", "autoscoring": "自动评分", "enablescoring": "启用评分", "flowcannotdelete": "流程被引用不可删除", "scoringtemplate": "评分模板", "disablecommettip": "回复框或附件不为空时允许点击", "scorefavourable": "未在指定时间段内评分，系统自动好评", "mobilereporttip": "该服务仅支持移动端上报", "flowname": "流程名称", "relform": "关联表单", "enableattachments": "启用附件", "enabledesc": "启用描述", "flowrult": "流转规则", "flowrulttip": "流程未关联表单，无法配置组合条件，请前往", "relchange": "关联变更", "changesteptodotip": "变更步骤设为待办", "changestepsoptip": "即变更（sop）步骤激活时，将该步骤设为待办", "enablechangetodotip": "即变更处理节点激活时，将所有变更步骤设为待办", "enablechange": "变更步骤激活时", "readychange": "变更就绪时", "successjudgmenttip": "默认按状态码判断，2xx和3xx表示成功.如果非默认，则需要配置集成出参作为判断条件", "failurejudgmenttip": "默认按状态码判断，4xx和5xx表示失败", "extcallnoparamtip": "外部调用没有回参，不允许自定义", "echotemplate": "回显模板", "callbackornot": "是否回调", "intervaltip": "定时检测需要配置检测时间间隔和状态查询接口地址，系统会定时调用状态查询接口，直到获得正确结果为止。", "userfilter": "处理人过滤", "emptynotlimitusertip": "不填写时，不限制处理人范围", "formscene": "表单场景", "norelformtip": "当前流程未关联表单，请先到", "noformscenetip": "未选择表单场景时，使用默认场景", "autocirculation": "自动流转", "targetparamsvalue": "目标参数赋值", "targetparamsvaluetip": "若选中的组合工具已预设执行目标、连接协议、执行用户、分批数量，此处不可修改已预设的参数", "jobparamsvalue": "作业参数赋值", "formparamsvalue": "表单赋值", "formparamsvaluetip": "将当前自动化编排中，[native/setenv]工具导出的参数，赋值到表单中，以便下游点引用", "prestepexportparam": "上游出参", "prestep": "前置步骤", "assigngoals": "指派目标", "assignscope": "指派范围", "formvalue": "表单值", "nouserformitemtip": "当前关联表单无可选组件（用户选择器或者下拉框），请前往", "reviserelform": "修改关联表单", "dispatcher": "分派器", "matchall": "全部匹配", "matchsort": "顺序匹配", "preuserappoint": "由前置步骤处理人指定", "copypreuser": "复制前置步骤处理人", "autoapproval": "自动审批", "autoapprovaltip": "把下游节点分派的处理人看作一个数组（不管是单人还是多人），当下游处理人数组，包含上游的实际处理人时，下游自动审批", "actionsetting": "动作设置", "getsertypeinfofail": "获取服务类型信息失败：", "savefail": "保存失败：", "empty": "空", "programarhelp": "语法帮助", "formaterror": "文件名为{target}格式不正确", "changenodestatus": "改变节点状态", "changelinename": "改变线名称", "menutype": "菜单类型", "allservicetype": "所有服务类型", "slatags": "SLA标签数", "slapolicy": "SLA策略数", "maxaging": "最大服务时效top3（h)", "minaging": "最小服务时效top3（h)", "titleexisted": "知识标题已存在", "rollbackto": "回退至", "selectservice": "选择要转报的服务", "changeto": "改为", "move": "移除", "triggertime": "{target}时触发", "meetcondition": "满足条件", "matched": "已匹配", "notmatch": "未匹配", "templatereplace": "模板变量替换", "selectchangetemplate": "选择变更模板", "variablerequire": "变量\"{target}\"必填", "pausechange": "暂停整个变更", "ignorecurrentstep": "忽略当前步骤并流转到下一步", "cancelchangestep": "取消变更步骤", "deadlinetime": "{target}截止", "unassigned": "未分派", "selectflowpath": "选择流转路径", "assigntosth": "指定\"{target}\"的处理人", "viewtask": "查看工单", "continuereport": "继续上报", "backtoservicelist": "返回服务目录", "origintasktitle": "原工单标题", "targettasktitle": "目标工单标题", "relateknowledge": "相关知识", "relknowlege": "关联知识库", "reportinghistory": "上报历史", "formcommonitem": "表单普通组件", "formtableitem": "表单表格组件", "automaticprocessing": "自动处理", "recreatenewjob": "回退新建作业", "jobpolicy": "作业策略", "singlecreatejob": "单次创建作业", "triggertiming": "触发时机", "notificationpolicysettings": "通知策略设置", "templateparametermapping": "模板参数映射", "targetparams": "目标参数", "selecttask": "选中工单", "savedraft": "保存草稿", "signreport": "签报", "signreporttip": "所有审批流第1个步骤，均为创建签报步骤，不支持修改。创建签报的处理人，为流程中EOA节点的处理人，在流程管理中设置。", "approvalpolicy": "审批策略", "eoadealwithusertip": "模板设置处理人后，编辑流程时中不可修改处理人", "approvalstep": "审批步骤", "approvalprocess": "审批流", "approvalprocesstip": "在【创建签报】节点之外，至少要添加一个节点", "matchmapping": "匹配映射", "approvertip": "在下方审批流填写步骤审批人", "eoatemplate": "EOA模板", "eoaautostart": "自动创建签报", "eoaautostarttip": "当处理人唯一、审批模板唯一，且审批模板中的每个审批步骤均已指定审批人时，自动创建签报", "formmap": "表单映射", "subprocess": {"waitdesc": "同步：所有子流程完成后父流程才允许继续流转", "formmap": {"parent": "父流程表单", "sub": "子流程表单", "catalogtitle": "服务目录-{target}"}, "tab": "子流程"}, "manualcirculation": "手动流转", "eoapassedforwardedto": "审批通过时流转至", "eoanopassedforwardedto": "审批不通过时流转至", "eoapassedforwardedtonode": "审批通过时流转节点", "eoanopassedforwardedtonode": "审批不通过时流转节点", "jobtextparamsvaluetip": "特殊用法：使用工单号、步骤ID等工单信息，映射为作业参数<br><br>样例：输入${DATA.serialNumber}_${DATA.stepId}，表示将【工单号】【_】【步骤ID】拼接，映射为作业参数<br><br>可选参数：<br>&nbsp;&nbsp;&nbsp;&nbsp;工单号-${DATA.serialNumber}<br>&nbsp;&nbsp;&nbsp;&nbsp;步骤ID-${DATA.stepId}<br>&nbsp;&nbsp;&nbsp;&nbsp;步骤处理人-${DATA.stepWorker}", "jobscenariovalue": "工具场景", "customexpression": "自定义表达式", "region": "地域", "processtaskcollection": "工单集合", "phase": "·阶段", "channel": "通道", "saveandstart": "保存并开始", "createcollection": "创建集合", "taskinformation": "工单信息", "workcentertheadppolicy": "控制该分类默认表头顺序以及是否显示", "selectsteptimelinetip": "按选的步骤，展示步骤内的时间线", "exportparameters": "[native/setenv]导出参数，环境变量的作用域要设置为所有节点", "eoaviewstep": "审批时查看步骤", "complatenode": "流转节点", "totaljobnumtarget": "总共{target}个作业", "runningcounttarget": "进行中{target}个", "completedcounttarget": "已完成{target}个", "failedcounttarget": "已失败{target}个", "changecreatewithouthandler": "请为变更创建节点关联变更处理节点", "viewauth": "查看授权", "viewauthtooltip": "当前登录人必须同时满足：在当前目录及所有父子目录的授权范围内，才能查看通过当前目录下服务创建的工单。", "reportauthtooltip": "当前登录人必须同时满足：在当前目录、以及所有父子目录和服务的授权范围内，才能通过当前目录下的服务创建工单。", "formtag": "表单标签", "formtagtip": "当标签为空时：表单组件为主场景的所有表单组件；当标签不为空时：表单组件为所选标签配置的表单组件。", "stepfilter": "流程步骤", "stepdesc": "过滤已激活的步骤", "savetransfer": "保存并转交", "savetransfertip": "保存当前表单并转交", "thirdpartyapi": "第三方回调接口", "reactivatestep": "重新激活步骤", "seeprocessconfig": "查看当前工单流程配置", "editprocessconfig": "编辑当前工单流程配置", "stepishandledby": "步骤处理人为", "sercalendar": "服务日历"}, "autoexec": {"addrootdirectory": "添加根目录", "editdirectory": "编辑目录", "deletedirectory": "删除目录", "deleteservice": "删除服务", "editservice": "编辑服务", "addservice": "添加服务", "searchservice": "搜索服务", "citecatalognodelete": "已被引用的目录不可删除", "citeservicenodelete": "已被引用的服务不可删除", "servicedescription": "服务说明", "batchsetting": "分批设置", "batchquantity": "分批数量", "executetarget": "执行目标", "executeaccount": "执行账号", "jobparam": "作业参数", "combinationtool": "组合工具", "citeform": "引用表单", "roundcountdescrition": "将执行目标按数量等分为N个批次，先后执行", "setbantchnumbernoupdate": "组合工具已设置分批数量，不可修改", "immediateexecution": "立即执行", "jobname": "作业名称", "otherparam": "其他参数", "customtoollibrary": "自定义工具库", "toollibrary": "工具库", "hasauthinfo": "拥有权限的用户可在组合工具中添加分类下的自定义工具或工具库工具", "hasauthtip": "拥有权限的用户可在组合工具中审核分类下的版本", "auditauthorization": "审核授权", "libraryusageinstructions": "工具库使用说明", "alltemplates": "所有模板", "directorytool": "工具目录", "resourcelock": "资源锁", "controlpanel": "控制台", "exportjob": "导出作业", "copyjob": "复制作业", "validatejobtip": "确认验证当前作业？作业验证通过后不能再做任何操作。", "loghaswarninfo": "日志中存在告警信息", "phasehastips": "阶段中存在状态为{target}的执行节点", "environmentvariable": "环境变量", "paramsdetail": "参数详情", "paramsname": "参数名", "paramsvalue": "参数值", "skipallsuccessignorenode": "跳过所有已成功、已忽略节点", "rerunallnode": "重跑所有节点", "operationlevel": "操作级别", "uploadTooltip": "预置参数集信息无法导入，若导入工具中引入了预置参数集，导入后请自行补充", "timingjob": "定时作业", "timingscheduling": "定时调度", "timesetting": "时间设置", "globalparameter": "全局参数", "displayname": "显示名", "valuetype": "值类型", "associatedobject": "关联对象", "presetparameterset": "预置参数集", "toolparameter": "工具参数", "associatedtool": "关联工具", "owningsystem": "所属系统", "screeningmode": "筛选方式", "inputtext": "输入文本", "inputtextdesc": "按指定格式，手动输入单个或多个节点信息", "filterdesc": "创建过滤器，选择指定范围的节点作为执行目标", "nodedesc": "自定义查询条件，过滤并勾选节点作为执行目标", "citejobparamdesc": "引用作业参数作为执行目标", "citeupstreamparamdesc": "引用上游节点输出参数作为执行目标", "upstreamparameter": "上游参数", "assetstatus": "资产状态", "subordinatedepartment": "所属部门", "maintenanceperiod": "维护期", "executionrecord": "执行记录", "chooseonecombinetooldesc": "至少选中一个组合工具", "configdesc": "配置是一段javascript代码，需要定义包含methods等属性的对象，例如：", "templatedesc": "模板需要符合vue模板语法规范，必须指定一个根标签，一般是div，支持所有iView组件，也支持以下内部组件：", "testdatajson": "测试数据需要符合json格式", "parentnodeid": "父节点id", "associateacustomtool": "关联自定义工具", "disassociatecandeletecatalog": "当前目录或其子目录已关联自定义工具，解除关联后，才可删除此目录", "bysubcatalogalldeletetips": "删除当前目录时，其所有子目录将同时被删除", "ipformattip": "主机类目标输入格式为 IP，服务类目标输入格式为IP:PORT，数据库类目标格式为IP:PORT/SID，一行一个目标,最多支持输入1000个目标", "viewalltarget": "查看更多{target}个目标", "choosetagasnodetip": "选择标签过滤节点，作为执行目标", "jobparamnosettingtip": "作业参数中未设置【节点信息】参数", "targetip": "目标名称、ip", "inspectstatus": "巡检状态", "batchexportdata": "批量导入数据", "batchedittip1": "每一行代表一个选项,且每一行的值不可以重复；", "batchedittip2": "值和显示文案用“,”分隔，可以只有值，显示文案会自动补充；", "batchedittip3": "数组长度不可大于500", "verifydifferentvalues": "每行对应的值不可相同", "datasourcesetting": "数据源设置", "selectionrange": "选择范围", "filterchoosenodedesc": "自定义查询条件，过滤并勾选节点", "setfilterdesc": "设置过滤器，缩小可选节点范围", "selectnode": "选择节点", "static": "静态", "filetype": "文件类型", "tool": "工具", "freeparameter": "自由参数", "failurestopinfo": "失败停止：任一目标执行当前工具失败后，该目标停止执行后续工具，其他目标正常执行当前阶段的所有工具后，停止执行下一阶段", "failurecontinueinfo": "失败继续：任一目标执行当前工具失败后，该目标跳过当前工具继续执行后续工具及阶段，其他目标不受影响", "satisfiedexecution": "满足执行", "otherwiseexecute": "否则执行", "tooldeleted": "当前工具已被删除", "tooldescription": "工具描述", "parameterenglishname": "参数英文名", "parameterchinesename": "参数中文名", "controltype": "控件类型", "editable": "可编辑", "toolnamedescription": "工具名称、描述", "customtool": "自定义工具", "timingplan": "定时计划", "planstarttime": "计划开始时间", "planendtime": "计划结束时间", "executetargettips": "仅支持选择包含 test 标签的资产作为执行目标", "selectcombinationtool": "选择组合工具", "job": "作业", "jobwarninginfo": "作业中存在告警信息", "jobignoreinfo": "作业中存在状态为 已忽略 的执行节点", "triggertype": "触发方式", "toolclassification": "工具分类", "executionsituation": "执行情况", "jobsource": "作业来源", "operator": "操作人", "executiontime": "执行时间", "taskexpirationtip": "为防止任务刚提交就过期，只允许选择5分钟后的时间。", "takeoverjob": "接管作业", "factoryclassnodelete": "当前分类是出厂默认分类，不可删除", "nosavetip": "您有未保存的修改", "scriptvalidsuccess": "脚本校验成功", "deletelastversiontip": "当前自定义工具仅有一个版本，删除此版本将导致删除当前自定义工具。", "draftname": "草稿名称", "createversion": "创建版本", "islibraryfile": "是否库文件", "risklevel": "风险等级", "jobdetailspecialinfo": "用于在作业详情页面展示个性化数据", "pipeline": "流水线", "submitnewversionwait": "提交了新版本，待", "deletedversion": "删除版本", "noexecuteauthrelateadmin": "无执行权限，请联系管理员！", "activecombinetooltip": "当前组合工具为禁用状态，请先激活组合工具后再执行", "stagegroup": "阶段组", "presetparameter": "预置参数", "maintainer": "维护人", "setinputcondition": "设置：请输入条件", "phasedeletetip": "已被删除，请在组合工具中清理该工具并保存", "phasesetpresetparamtip": "设置:请选择预置参数集", "phasesetfreeparamtip": "设置:自由参数数据填写不完整", "phasesetinputparamtip": "设置:输入参数数据填写不完整", "scriptparser": "脚本解析器", "scriptcontent": "脚本内容", "rejectreason": "驳回了审批，原因：", "selectcomparativeversion": "请在下表中选择需要对比的版本", "toolreadyaudit": "工具待审核", "backtolibrary": "返回工具库", "relatecombinationtool": "关联组合工具", "dependenttool": "依赖工具", "inputparamerror": "输入参数数据填写不正确", "outputparamerror": "输出参数数据填写不正确", "selectparser": "解析器：请选择", "inputscriptcontent": "脚本内容：请输入内容", "inputfreeparam": "自由参数：请输入内容", "scriptcontenterror": "脚本内容：不符合规则", "closefreeparamtip": "关闭自由参数时，已设置的自由参数将被删除，是否继续关闭？", "ignorephaseconfirm": "确认忽略当前阶段？", "jobruntip": "作业重跑或继续运行时，将跳过已忽略的节点", "paramdownload": "参数下载", "standardoutput": "标准输出", "runscript": "运行脚本", "executionresult": "执行结果", "runrecord": "运行记录", "resetnodeconfirm": "重置节点将会使节点状态变为“待运行”，是否重置节点", "resetnodescene": "重置节点适用于以下两种场景：", "waitrunnodetip": "节点需要重跑，重置为“待运行”状态后，可重跑节点。", "malfunctionrerunjob": "本平台出现故障，导致所有节点停止运行，可重置所有节点，重跑当前作业。", "ignoreselectednode": "是否忽略选中节点", "deletnodetip": "选中节点包含待运行、运行中、已成功的节点，不可执行忽略操作，是否剔除待运行、运行中、已成功节点，并忽略其他节点", "rerunselectednodeconfirm": "是否重跑选中节点", "noenvproducts": "暂无环境制品", "selectednodeinclude": "选中节点包含", "ignoreselectednodeconfirm": "确认忽略选中节点？", "deletenodecontinue": "节点，是否剔除这些节点并继续？", "ignorecount": "<b>{target}</b>个忽略", "pendingcount": "<b>{target}</b>个待运行", "successcount": "<b>{target}</b>个已成功", "runningcount": "<b>{target}</b>个运行中", "noignorenode": "当前选中节点没有可忽略的节点，请重新选择", "nodedetail": "节点详情", "reexecutecurrenphase": "跳过所有已成功，已忽略节点，重新执行当前阶段？", "executephase": "执行阶段", "addtotarget": "添加到目标", "notaddtotarget": "未添加目标", "importsuccesstarget": "成功导入{target}个目标；", "formaterror": "格式不正确；", "targetrepetition": "目标重复；", "issaveexecutetarget": "是否保存执行目标", "notmatchexecutetarget": "当前过滤器下，未找到匹配的执行目标", "addaccount": "添加账号", "thefollowingobjectives": "以下目标：", "addbindaccount": "添加绑定账号", "notfoundassetstip": "未在资产清单中找到对应资产，执行时将跳过上述目标", "ignoretargettoassetpage": "执行时将跳过上述目标，可前往资产清单页面", "nousertoaccountpagesetting": "未找到“连接协议”:{protocol},执行用户:“{executeuser}”的账号，可前往账号管理页面", "tagentnoexecuteusertoassetmanage": "未找到“连接协议”:{protocol}的账号，执行时可能导致异常，请前往‘系统配置->Tagent管理’核查tagent状态是否已连接", "noexecuteusertoassetmanage": "未找到“连接协议”:{protocol},“执行用户”:{executeUser}的账号，执行时可能导致异常，需要绑定账号,可前往资产清单页面", "publishcombinetool": "发布组合工具", "executecount": "执行次数", "starttoexecute": "启动后已执行：", "lastactivetime": "最近激活时间", "lastcompletetime": "最近完成时间", "pleaseinputversionname": "请输入版本名称", "addjob": "添加作业", "batchcountdisabledesc": "将执行目标按数量等分为N个批次，先后执行。", "choosethetime": "选择时机", "nowriteusertooltip": "若此处不填用户，当前阶段默认继承组合工具执行用户，若此处填写用户，当前阶段将采用此处填写的执行用户；执行时，不可修改执行用户", "batchcountprioritydesc": "将执行目标按数量等分为N个批次，先后执行，阶段设置的分批数量优先级高于组合工具或作业中设置的分批数", "executeTooltip": "若此处不填写执行目标，当前阶段默认继承组合工具执行目标，若此处填写执行目标，当前阶段将采用此处填写的执行目标；执行时，不可修改阶段的执行目标", "valueshowtext": "值,显示文案", "ifblockconditiongramdescription": "IF-Block条件语法说明：", "comparativesupport": "比较支持：", "filedetection": "文件检测：", "filedelink": "（对应文件、目录、存在、软连接判断）", "logicaloperation": "逻辑操作：", "availableenvironmentvariables": "可用的环境变量：", "applyname": "应用名", "modulename": "模块名", "envname": "环境名", "instanceip": "实例IP", "instanceport": "实例端口", "instanceprotocolport": "实例协议端口", "versioncatalog": "版本制品目录", "forexample": "例子：", "setfilterexecutelimitdesc": "设置过滤器后，执行时只能在过滤器范围内选择执行目标", "jobparamnotconfignodeinfoparamdesc": "作业参数中未设置【节点信息】类型的参数", "allphasesetconfigtargetaddjobdesc": "若所有阶段均已配置目标,创建作业时用户无需再指定目标", "settodefaultscenario": "设为默认场景", "thisscenariohasconfigured": "此场景已配置", "pleaseselectatleastonephase": "请至少选择一个阶段", "exportnode": "导出节点", "enterthenodenameoripaddress": "请输入节点名称或ip", "skipallsuccessoperationcontinue": "跳过所有已成功操作，继续执行其它操作？", "actuatorinformation": "执行器信息", "sqlmanifest": "SQL清单", "savejob": "保存作业", "isdataproperties": "是数据中的属性", "pleaseselectcategory": "请选择分类", "pleaseselectdirectoryname": "请选择目录名称", "pleaseselectuploadfile": "脚本内容：请上传附件", "supportonlytarfile": "仅支持tar文件", "scriptcontenttips": "帮助：\n 工具执行是通过标准的命令行参数来传参。\n 输入参数：\n 1）具备参数名的参数：\n   option名称就是参数名，option值就是参数值。\n 2）没有参数名的参数：\n   对应工具的自由参数，通过参数序号来获取参数。\n 不同的编程语言如何处理命令行参数，具体请查看相关的demo。\n 输出参数：\n 通过把输出参数写入当前作业工作目录下的json文件ouput.json进行参数的输出。\n 平台提供的不同语言的输出封装，具体请查看相关的demo，例如：\n Python： \n #runner类型工具\n #import AutoExecUtils\n #target类型工具\n from lib import AutoExecUtils\n out = {}\n out['outtext'] = 'this is the text out value'\n AutoExecUtils.saveOutput(out) \n Perl：\n use AutoExecUtils;\n my $out = {};\n $out->{outtext}     = 'this is the text out value';\n AutoExecUtils::saveOutput($out); \n Bash：\n outtext='this is the text out value'\n if [ ! -z '$OUTPUT_PATH' ]; then\n     cat <<EOF >'$OUTPUT_PATH'\n {\n     'outtext':'$outtext'\n }\n EOF\n fi\n 退出码标识：\n 只要ExitCode不等于0则代表异常退出。\n注意：\n 如果需发起子进程，执行完毕后请注意关闭标准输出和错误输出，否则父进程可能无法正常退出。", "speedlevel": "速度级别", "workthread": "工作线程", "loadedjob": "已加载作业", "jobmodule": "作业模块", "jobhandler": "作业处理器", "servicehasexpired": "服务已失效", "currenttoolisalreadydependentonanothertool": "当前工具已被其他工具依赖，", "deletefailedcombinatetoolpresetcitetips": "删除失败，请检查是否已被组合工具或预置参数集引用", "usedtooldesc": "当前类型已被工具引用，无法删除", "targetjoberror": "{target}创建作业失败", "toolparamstooltip": "工具参数是已关联的工具的参数集合。来源于自定义工具库的参数可增加、删除和修改，来源于工具库的参数无法增加、删除和修改。", "combophasexpired": "组合工具已失效", "pleasereedit": "请重新编辑后，提交审核", "abortjob": "中止作业", "runnergroupdeprecatedtips": "作业执行器组参数已失效，请重新点击确认并保存流程", "loopitem": "循环项", "loopoperation": "循环执行", "loophelp": "按照循环项循环执行，多个值使用空格隔开，例如：'itemA' itemB \"itemC\"，循环项会设置到“循环项名”环境变量中循环使用", "loopitemname": "循环项变量", "loopitemhelp": "给循环项变量设置环境变量，在循环体内使用", "setinputloopitemvar": "设置：请输入循环项变量", "setinputloopitems": "设置：请输入循环项", "setinputloopoperations": "设置：请输入至少一个工具", "runnergrouptagprocesstips": "如果需要表单赋值，请在组合工具设置“执行器组标签”为“作业参数”类型", "runnergroupprocesstips": "如果需要表单赋值，请在组合工具设置“执行器组”为“作业参数”类型", "resetrunnerphase": "是否重置阶段", "runnerGroupTooltip": "若此处不填写执行器组，当前阶段默认继承组合工具执行器组，若此处填写执行器组，当前阶段将采用此处填写的；执行时，不可修改阶段的执行器组", "isfirenext": "是否激活下一个组", "inputnodelimit": "数量不能大于1000", "parall": "并发数量", "paralldesc": "将执行目标按并发数量先后执行。", "jobrecord": "自动化作业记录", "jobcount": "自动化作业数"}, "framework": {"roleauth": "创建完角色立即授权", "rolename": "角色名", "roledesc": "角色描述", "rolecount": "角色数量", "usercount": "用户数量", "teamcount": "分组数量", "teamname": "分组名称", "authname": "权限名称", "belongmodule": "所属模块", "authdesc": "权限描述", "calendar": "排班", "inheritselected": "继承选中", "selectsubnode": "选中子节点", "printsnapshot": "打印快照", "runtimesort": "按执行时间降序", "timecostsort": "按耗时降序", "monitorlist": "监控语句列表", "datapool": "数据库连接池", "totalconnections": "总连接", "activeconnections": "活动连接", "awaitingconnections": "等待连接", "freeconnections": "空闲连接", "sqlsstatement": "SQL语句", "settingsql": "设置SQL", "addmonitor": "增加监控", "delmonitor": "删除监控", "corelibrary": "核心库", "subsidiarylibrary": "附属库", "datafootprint": "数据占用空间", "indexfootprint": "索引占用空间", "datafreespace": "数据空闲空间", "targetdayago": "{target}天前", "selectintgn": "编辑选择集成", "upconfigfile": "请上传配置文件", "matrixuniquekey": "矩阵唯一标识", "integrationsetting": "集成设置", "cidata": "模型数据源", "matrixprimarykey": "矩阵主键", "emailtest": "邮件测试", "smtphost": "smtp主机", "smptport": "smtp端口", "head": "请求头", "inputtrans": "输入转换", "outputtrans": "输出转换", "dataspecification": "数据规范", "reqsetting": "请求设置", "requestFrom": "请求来源", "syncreport": "同步记录", "syncdata": "同步数据", "cronexpression": "定时执行", "xmlconfig": "配置内容", "syncmode": "同步模式", "appendmode": "追加模式", "replacemode": "替换模式", "syncnow": "立即同步", "isexpire": "是否过期", "expired": "已过期", "notexpired": "未过期", "inputtype": "输入控件", "iscondition": "作为条件", "copyversion": "复制激活版本", "subscibe": "订阅管理", "topic": "主题管理", "subscribe": "订阅", "isdurable": "订阅方式", "dursubs": "持久订阅", "tempsubs": "临时订阅", "batchupgrade": "批量升级", "batchreboot": "批量重启", "batchresetcred": "批量更新密码", "installpackage": "安装包管理", "tagentinstall": "Tagent安装", "connected": "已连接", "notconnected": "未连接", "reqparamstip": "标识为必填参数", "logdownload": "日志下载", "upgradeversion": "升级版本", "pkgversion": "版本号", "ostype": "OS类型", "runnergroup": "代理组", "tagentsearchpla": "包含IP/名称/OS版本", "includeip": "包含IP", "osversion": "OS/OS版本", "osbit": "CPU架构", "mem": "内存", "actuator": "执行器管理", "runnerlist": "Runner列表", "ipnetwork": "网段IP/子网掩码", "runnercount": "Runner数量", "upgrecord": "升级记录", "upgagentcount": "升级agent数量", "upgtime": "升级时间", "sourceversion": "原版本", "targetversion": "目标版本", "upgdetails": "升级详情", "nettyport": "NETTY端口", "proxygroup": "代理组名", "mask": "位数", "tagentlist": "tagent列表", "selectpackage": "选择已有安装包", "autoinstall": "自动安装", "manualinstall": "手动安装", "manualuninstall": "手动卸载", "ipportselect": "根据IP:PORT选择", "networkselect": "根据网段选择", "tagentnotfind": "tagent心跳没有找到", "runnertnotfind": "runner没有找到", "viewdownload": "查看完整结果请下载", "invertedorder": "倒序", "positiveorder": "正序", "apihelp": "接口帮助", "selectyear": "已设年份", "calendarinfor": "排班信息", "workinghours": "工作时段", "i": "增", "d": "删", "c": "改", "q": "查", "o": "作", "activedversion": "激活版本", "rowreaction": "行联动", "formwidth": "表单宽度", "saveothernewversion": "另存为新版本", "customitem": "自定义组件", "layoutwidget": "布局组件", "automationwidget": "自动化组件", "cmdbwidget": "配置管理组件", "theme": {"themecolor": "主题色", "headcolor": "头部颜色", "tablecolor": "表格颜色", "formcolor": "表单颜色", "menucolor": "左侧菜单颜色", "otherscolor": "其他颜色", "dashboardcolor": "仪表板颜色", "logoimg": "LOGO 图片", "themehovercolor": "主题色鼠标经过颜色", "themeactivecolor": "主题色激活颜色", "headbg": "头部背景", "headbghover": "头部背景鼠标经过色值", "tablestripecolor": "表格斑马颜色", "tablehovercoler": "表格移上去颜色", "tableseleccolor": "表格选中颜色及主色弱化", "thcolor": "表头颜色", "checkboxcolor": "多选框选中背景色", "selectcolor": "下拉选中和鼠标经过色值", "swithbg": "开关底色", "formdisabledcolor": "多选和单选(禁用,选中状态)", "menuselectcolor": "左侧菜单选中颜色", "menuhovercolor": "左侧菜单鼠标经过颜色", "graycolor": "辅助色", "warnbg": "警告背景颜色", "chartcolor": "图表颜色", "valuebg": "值图背景", "valuetextcolor": "值图文字", "logosetting": "LOGO图片配置", "customlogo": "自定义LOGO"}, "triggeraction": "仅看有动作的", "nottriggeraction": "仅看无动作的", "notifyhandler": "通知方式", "tovouser": "接收人", "nextfiretime": "下次执行时间", "reqcondition": "请把条件填写完整", "sysparamsnotedit": "系统参数不允许操作", "freemarkerhelp": "freemarker语法帮助", "apiaccesstime": "访问记录保存期限", "apitest": "接口测试", "formrequesttype": "表单模式", "jsonrequesttype": "json模式", "timeout": "请求时效", "qps": "访问频率", "apirecord": "接口调用记录", "calltime": "调用时间", "direction": "排列方向", "openmode": "打开方式", "jumpaddress": "跳转地址", "hrefblank": "新窗口", "hrefself": "当前窗口", "later": "大于", "earlier": "小于", "laterandequal": "大于等于", "earlierandequal": "小于等于", "equal": "填写当天", "lessthan": "填写当天前", "greaterthan": "填写当天后", "othersdate": "指定日期", "selectlevel": "下拉级数", "grade": "级", "leveloptions": "级选项", "selectmode": "选择方式", "matrixattr": "矩阵属性", "pcshowtd": "PC端显示列", "mbshowtd": "移动端显示列", "searchcondition": "搜索条件", "extraattr": "扩展属性", "dialogmode": "弹窗选择", "normalmode": "直接选择", "tablematrix": "表格矩阵", "showtextfields": "显示文字字段", "thsetting": "表头设置", "defalinenum": "默认行数", "compval": "组件值", "insetdown": "向下插一行", "insetup": "向上插一行", "insetleft": "向左插一列", "insetright": "向右插一列", "delrow": "删除当前行", "delcol": "删除当前列", "layoutmethod": "布局方式", "fixedratio": "固定比例", "fixedpixels": "固定像素", "canvaswidth": "使用画布宽度", "cusscenename": "自定义场景名称", "impactrow": "影响行", "hiderow": "隐藏行", "displayrow": "显示行", "reactionsetting": "联动设置", "targetline": "第{target}行", "inputdata": "输入数据", "formcomplistlable": "组件列表（点击复制组件uuid）", "outputdata": "输出数据", "simulatedsubmission": "模拟提交", "assignment": "赋值", "inheritscenesetting": "继承默认场景配置", "hideclearval": "隐藏时清空值", "ishasvalue": "是否有返回值", "compconfigtemp": "组件配置模板", "comptemp": "组件模板", "userselect": "用户选择", "treeselect": "树型下拉框", "tableselect": "表格选择", "tableinput": "表格输入", "formdivider": "分割线", "cubeselect": "矩阵选择", "formcollapse": "折叠面板", "formckeditor": "富文本框", "formcascader": "级联下拉框", "changepriority": "修改优先级", "formcientitymodify": "配置项修改", "formcientityselector": "配置项选择", "filestandard": "附件规范请参考", "cusvalid": "自定义校验", "timeneed": "时间需要", "dragformcomp": "请拖入表单组件", "datenotworktime": "日期不在工作时间范围内", "dateneed": "日期需要", "currdate": "当前填写日期", "valfieldmapping": "值字段映射", "showtextfieldmapping": "显示文字字段映射", "groupname": "用户组名称", "selectedchild": "已选中子节点", "unselectedchild": "未选中子节点", "selectedtarget": "选中目标", "notcancelauth": "继承角色权限，不允许取消授权", "filterbypermission": "按权限过滤", "filterbyrole": "按角色过滤", "importfromuserlist": "从用户列表导入", "importfromusergroup": "从用户组导入", "position": "职务", "parentpath": "上级路径", "groupleader": "分组领导", "subgroup": "下级分组", "roleid": "角色ID", "menberlist": "成员列表", "grouplist": "分组列表", "batchauth": "批量授权", "returntogrouplist": "返回分组列表", "continuecreate": "继续创建", "notips": "不再提示", "saveusergroupchange": "是否保存对当前用户组的更改？", "viewuser": "查看用户", "saveuserchange": "是否保存对当前用户的更改？", "backtouserlist": "返回用户列表", "editinsetting": "可在个人设置中修改", "userlevel": "用户级别", "setpwd": "设置密码", "confirmpwd": "确认密码", "pleaseconfirmpwd": "请确认密码", "inputphonenumber": "请正确输入电话号码", "searchcontent": "搜索内容", "grouprole": "分组角色", "userlist": "用户列表", "pwdnotsame": "两次密码前后输入不一致", "userpermission": "用户权限", "changeavatar": "修改头像", "previewavatar": "头像预览", "uploadagain": "重新上传", "missionauth": "任务授权", "authservise": "授权服务", "startlargethanend": "开始时间不能大于结束时间", "authuserisrepeat": "授权用户不能重复", "authserviceisrepeat": "授权服务不能重复", "recreate": "重新生成", "modifypwd": "修改密码", "custom": "个性化", "popupalert": "弹窗提醒", "defaultitem": "默认选项", "token": "令牌", "currentpwd": "当前密码", "newpwd": "新密码", "pleaseconfirmnewpwd": "请确认新密码", "widgettype": "组件类型", "prioritystatistics": "优先级统计", "valuechart": "数值图", "itsmworkorder": "itsm工单", "scope": "适用范围", "optionalrange": "可选范围", "dataasyncconfirm": "确认执行数据同步吗？", "configexample": "配置范例", "parsing": "语法分析中...", "saveexpire": "同步记录保存期限", "userselector": "用户选择框", "enumselector": "枚举选择框", "flowlist": "流程引用列表", "deletealert": "删除提示", "deleteversionconfirm": "确定要删除<b>{target}版本</b>的表单", "deletecurrentversionconfirm": "确定要删除<b>当前版本</b>的表单", "extendwidget": "扩展组件", "widgetinfouncomplete": "组件信息填写不完整", "linkagewidgetuncomplete": "表单设置里的组件联动填写不完整", "getformdatafail": "获取表单数据失败", "errorinfo": "错误信息", "multi": "多个", "rebuildconfirm": "重建确认", "rebuilddesc": "是否确认重建当前类型的检索索引？", "incrementalrebuild": "增量重建", "allrebuild": "全部重建", "accessaudit": "访问审计", "requestadress": "请求地址", "paramtype": "参数类型", "requestparam": "请求参数", "sendingparam": "发送参数", "transferresult": "转换结果", "requestfail": "发送测试请求失败", "paramformaterror": "参数格式不符合json格式", "noparam": "没有请求参数", "grammarhint": "语法提示", "connecttimeout": "连接超时", "readtimeout": "读超时", "paramcheckdesc": "参数说明用于对外提供调用帮助，如需要系统根据参数说明进行参数校验，请启动以下选项。", "checkenable": "激活校验", "aftercheckdesc": "激活校验后，非参数说明列表中的参数将会被丢弃。", "transfertest": "转换测试", "output": "输出", "authorizedto": "授权给", "issuedate": "签发日期", "expiredate": "失效日期", "serveceenddate": "服务终止日期", "usedmatrixdesc": "当前矩阵已被表单引用，无法修改模型数据源", "acmenulist": "菜单列表", "firstlevelmenu": "一级菜单", "parentmenu": "上级菜单", "isenable": "是否启用", "defaultopen": "默认打开", "newtab": "新标签页", "selecticon": "选择图标", "iconselect": "图标选择", "childmenu": "子菜单", "formuncomplete": "表单填写完整!", "noticeeffitivetime": "公告生效时间", "nostarttimedesc": "若不指定开始时间，则公告即刻下发；若不指定结束时间，公告需要手动停止。", "nopopupdesc": "再次下发时，已查看过上次公告的用户将不再弹窗提醒。", "associationgroup": "关联组", "deleteconfirm": "确认删除", "externalauthen": "外部认证", "authenprotocol": "认证协议", "nameandip": "名称、ip", "unzipto": "解压到", "or": "或", "and": "且", "saverolechange": "是否保存对当前角色的更改？", "backtorolelist": "返回角色列表", "defaultpolicy": "默认策略", "defultpolicysetting": "默认策略设置", "syncsceneattr": "同步默认场景属性", "matrix": {"isoverride": "当前环境已存在同一矩阵，是否覆盖？"}, "corpid": "企业ID", "corpsecret": "应用的凭证密钥", "agentid": "企业应用ID", "smptsslenable": "使用SSL", "shutdown": "停机", "host": "服务器地址", "heartbeattime": "最近一次心跳时间", "heartbeatrate": "心跳频率", "heartbeatthreshold": "心跳阈值", "versionlog": "版本日志", "exporttable": "导出表格", "importtable": "导入表格", "excelinputtemplate": "表格输入模板", "formsubassembly": "表单子组件", "globalreadonly": "全局只读", "globalreadonlytip": "全局只读优先级高于组件内部的读写设置，打开全局只读开关后，场景内所有组件均为只读状态", "iscannotdeletenode": "存在子节点不可删除", "requiredselectedtips": "若当前组件设置为必填，且过滤后仅剩余一个选项，系统会自动选中该选项", "formtableinputercomponent": "表格输入组件", "tablecomponent": "表格组件", "internalmenu": "内部菜单", "custommenu": "自定义菜单", "internalmenuoperationtips": "内部菜单只能查看权限，不能新增编辑菜单", "hidetab": "隐藏选项卡", "commercialauth": "商业模块专属权限", "regiondispatcherapp": "业务系统", "regiondispatcherapptooltip": "请选择业务系统表单", "regiondispatchermatchtooltips": "分派器属性映射矩阵属性", "compkeyname": "组件英文名称", "writetime": "填写时间", "timevalue": "时间值：", "timevaluedesc": "0：表示“自定义|填写时间|日期”的值。", "positiveintegerdesc": "正整数：表示“自定义|填写时间|日期”的值增加相应时间（或根据选择时间单位增加）。", "neglectintegerdesc": "负整数：表示“自定义|填写时间|日期”的值减少相应时间（或根据选择时间单位减少）。", "writetimedesc": "填写时间：表示触发当前组件校验时的时间。", "fieldsconfiginfo": "根据字段映射配置，获取工单上报人相关信息", "jsscriptconversion": "js脚本转换", "strjoin": "字段拼接", "expressionplaceholder": "请填写ES5脚本，最后返回表达式的值value，范例：", "matrixsaveconfirm": "矩阵已被引用，是否确认修改？", "linkageassignment": "联动赋值", "conditionassignment": "条件赋值", "hideattrassignment": "隐藏属性赋值", "allowadd": "可新增", "allowdelete": "可删除", "mqtype": "消息队列类型", "notavailable": "不可用", "mqhandler": "消息队列组件", "showimportexporttemplatetableoptions": "显示导出/导入(模板、表格)选项", "componentnoexist": "组件不存在", "custommaxtrixselectaddbtndesc": "当选择自定义矩阵类型并新增数据时，下拉框将显示“+”按钮。", "showcomponentnamesintab": "显示拖入Tab选项卡内的组件名称", "hideheaderwhendataempty": "数据为空隐藏表头", "formsubassemblytabname": "选项卡名称", "formsubassemblytabnametip": "文本框、下拉框的值和隐藏属性，可作为选项卡名称", "tagentupgrade": "升级包管理", "tagentupgradepkg": "升级包", "selecttagentupgradepkg": "选择已有的升级包", "tagentuploadupgradepkg": "上传升级包", "versionremarks": "版本备注", "heartbeaturl": "后端服务器地址", "runnergrouptips": "登录认证的请求需要携带Header做规则表达式(注意表达式中header参数全小写，且前缀必须为{prefix})，如果表达式执行后的值为true则该执行器组生效，false和语法异常都不生效。如:", "batchdeletetagenttooltip": "已连接状态的tagent不能删除"}, "knowledge": {"document": "文档", "knowledge": "知识", "intellectualcircle": "知识圈", "approver": "审批人", "member": "成员数量", "documentcount": "知识数量", "knowtype": "知识分类", "knowtypename": "知识分类名称", "approve": "审批", "rejected": "驳回了申请，原因", "savedraft": "存草稿", "primaryclassification": "一级分类", "samedirectory": "同级目录", "navigationdirectory": "导航目录", "notresult": "无结果", "searchresult": "共为您搜索到{target}条结果", "outtopage": "连接到外部页面", "searchdockeyword": "搜索文档关键字", "selectinlink": "选择内部链接", "jumptolink": "跳转到外部连接：{target}", "viewdoc": "查看文档", "backknowlist": "返回知识列表", "confirmreleasenewvers": "确定发布新版本", "docamount": "文档数量", "tooltip": {"approver": "审批人拥有知识圈内的所有知识文档的查看、编辑、审批和删除权限", "member": "知识圈成员有权限查看、编辑该圈子里的所有知识文档", "documentcount": "该统计数量包括审批通过与未审批通过的知识数量", "sumbit": "文档待审核，您可以在文档的活动中查看审核进度", "template": "注：选中模板后，新模板将覆盖当前文档所有内容", "replace": "当前关键字包含链接，不允许替换", "replace1": "已经替换了{n}处文案，含链接的关键字不允许替换，已自动跳过", "replace2": "已经替换了{n}处文案", "search": "关闭搜索替换", "editdocumentapprove": "修改了共享文档【{target}】，待"}, "imageloadding": "正在加载图片", "imageloadfail": "图片加载失败", "editor": "编辑器", "leftalign": "居左", "rightalign": "居右", "imagedesc": "图片描述", "toinsidelink": "跳转到内部链接：", "sharefile": "共享文档", "waitto": "待", "norelateknowledge": "无相关知识", "strikethrough": "中划线", "uploadimages": "上传图片", "enterlinkdescription": "输入链接说明"}, "report": {"report": "报表", "reporttemplate": "报表模板", "reportname": "报表名称", "reportdetail": "报表详情", "sendplan": "发送计划", "editreport": "编辑报表", "addreport": "添加报表", "previewreport": "报表预览", "screen": "大屏", "screenmanage": "大屏管理", "textfield": "文本字段", "displayfield": "显示字段", "contentconfigexample": "内容配置范例", "datasourceconfigexample": "数据源配置范例", "taskid": "工单ID", "stepid": "步骤ID", "stepname": "步骤名", "copytemplate": "复制模板", "edittemplate": "编辑模板", "deletetemplate": "删除模板", "addtemplate": "新建模板", "conditionconfig": "条件配置", "datasourceconfig": "数据源配置", "contentconfig": "内容配置", "paramconfig": "参数配置", "isactive": "是否激活", "visits": "访问量", "control": "控件", "scheduledsending": "定时发送", "editsendplan": "编辑发送计划", "copysendplan": "复制发送计划", "newsendplan": "新建发送计划", "addsendplan": "添加发送计划", "nextsendingtime": "下次发送时间", "sendtimes": "发送次数", "sending": "发送中", "sendrecord": "发送记录", "canvassize": "画布大小", "alignguideline": "对齐辅助线", "selectedwidget": "已选组件", "refreshrate": "刷新频率", "returnrows": "返回行数（0代表不限制）", "tilingstyle": "平铺方式", "tiling": "平铺", "stretch": "拉伸", "borderspacing": "边框间距", "cornersize": "转角大小", "mainbordercolor": "主边框颜色", "subbordercolor": "副边框颜色", "motiondelay": "运动延迟", "motionoffset": "移动偏移", "coloralpha": "颜色alpha", "aolorbeta": "颜色beta", "highlightborder": "高亮边框", "doublecoloroverlap": "双色交叠", "rainbowfluorescence": "彩虹流光", "displayrange": "显示范围", "country": "国家", "province": "省份", "city": "城市", "area": "地区", "customcolor": "自定义颜色", "randomcolor": "随机颜色", "fontsize": "字符大小", "smoothcurve": "平滑曲线", "meter": "码表", "dialcolor": "表盘颜色", "pointercolor": "指针颜色", "centercolor": "中心颜色", "datafontsize": "数据字号", "datacolor": "数据颜色", "shape": "形状", "bubbleshape": "气泡形状", "bubblesize": "气泡大小", "circle": "圆形", "diamond": "钻石形", "triangle": "三角形", "pin": "水滴形", "rect": "正方形", "texturestyle": "贴图样式", "purecolor": "纯色", "dot": "圆点", "line": "斜纹", "wavelength": "水波长度", "innerradius": "内圆半径", "horizontallayout": "横向布局", "verticallayout": "纵向布局", "datalayout": "数据布局", "statisticfontsize": "统计数据字号", "statisticcolor": "统计数据颜色", "displayarea": "显示面积", "showtitle": "显示标题", "titlecontent": "标题内容", "showexplain": "显示说明", "explaincontent": "说明内容", "percentmode": "百分比模式", "showdata": "显示数据", "autoscroll": "自动滚动", "example": "范例", "sourcefile": "资源文件", "positions": {"topleft": "上居左", "top": "上居中", "topright": "上居右", "bottomleft": "下居左", "bottom": "下居中", "bottomright": "下居右", "lefttop": "左上角", "left": "左居中", "leftbottom": "左下角", "righttop": "右上角", "right": "右居中", "rightbottom": "右下角", "inner": "内部", "outer": "外部", "spider": "蜘蛛布局", "horizontalalign": "水平对齐", "autofit": "自适应大小"}, "axis": {"xcoordinate": "x坐标", "xcoordinatetitle": "x坐标标题", "ycoordinate": "y坐标", "ycoordinatetitle": "y坐标标题", "yaxisshow": "显示Y轴", "xaxisshow": "显示X轴", "yaxistitle": "Y轴标题", "xaxistitle": "X轴标题", "columnwidth": "柱子宽度", "showlegend": "显示图例", "legendposition": "图例位置", "legendlayout": "图例布局", "yfieldalias": "Y轴字段别名", "yfield": "Y轴字段", "xfield": "X轴字段", "xfieldalias": "X轴字段别名", "colorfieldalias": "颜色字段别名", "sizefieldalias": "大小字段别名", "showleft": "左显示", "showright": "右显示", "xleftsetting": "左X轴设置", "xsetting": "X轴设置", "ysetting": "Y轴设置", "xrightsetting": "右X轴设置", "valueinterval": "数值间隔", "valuefontsize": "数值字号", "valueangle": "数值角度", "tickline": "刻度线", "xaxis": "X轴线", "yaxis": "Y轴线", "axiscolor": "轴颜色", "dividinglinedisplay": "分割线显示", "verticaldividingline": "竖分割线", "dividinglinecolor": "分割线颜色", "dividinglinewidth": "分割线宽度", "borderline": "边框线", "valuealign": "数值对齐", "axismarginsetting": "坐标轴边距设置", "marginleftright": "左右边距(像素)", "marginleft": "左边距(像素)", "marginright": "右边距(像素)", "margintop": "顶边距(像素)", "marginbottom": "底边距(像素)", "xaxisalias": "X轴别名", "yaxisalias": "Y轴别名", "aliascolor": "别名颜色", "aliassize": "别名字号", "axisreverse": "轴反转", "textAngleX": "文字角度", "textspacing": "文字间隔", "scale": "缩放", "equal": "均分", "leftequal": "左均分", "rightequal": "右均分", "namefield": "名称字段", "colorfield": "颜色字段", "sizefield": "大小字段"}, "fontweight": {"name": "字体粗细", "bold": "粗体", "bolder": "特粗体", "lighter": "细体"}, "bubblesetting": {"name": "气泡设置", "fontminsize4pin": "最小半径", "fontmaxsize4pin": "最大半径"}, "position": {"left": "左边距", "top": "上边距", "widthplaceholder": "该容器在1920px大屏中的宽度", "heightplaceholder": "该容器在1080px大屏中的高度"}, "datatype": {"name": "数据类型", "staticdata": "静态数据", "bindstaticdata": "静态数据绑定", "binddynamicdata": "动态数据绑定", "dynamicdata": "动态数据", "refreshTime": "刷新时间(毫秒)"}, "chartsetting": {"barlinechart": "柱线图", "barchart": "柱状图", "barstackchart": "柱状堆叠图", "barcomparechart": "柱状对比图", "decoratepiechart": "装饰饼图", "staticdecoratepiechart": "静态装饰饼图", "funnelchart": "漏斗图", "dashboard": "仪表盘", "heatmap": "热力图", "gradientbarchart": "柱状图-渐变色", "stackstyle": "堆叠样式", "stackleftright": "左右堆叠", "stacktopbottom": "上下堆叠", "verticalshow": "竖展示", "horizontalposition": "横向位置", "verticalposition": "纵向位置", "layoutFront": "布局前置", "vertical": "竖排", "horizontal": "横排", "value": "数值", "valuesetting": "数值设定", "barvaluesetting": "柱体数值设定", "maxwidth": "最大宽度", "minheight": "最小高度", "dynamicheight": "动态高度", "transpose": "转置", "legendaction": "图例操作", "legendname": "图例名称", "legendwidth": "图例宽度", "brokenlinesetting": "折线设置", "markPoint": "标记点", "pointSize": "点大小", "area": "面积堆积", "areaThickness": "面积厚度", "lineWidth": "线条宽度", "barsetting": "柱体设置", "ringsetting": "圆环设置", "gradient": "渐变色", "0to30percentgradient": "0%~30%渐变色", "30to70percentgradient": "30%~70%渐变色", "70to100percentgradient": "70%~100%渐变色", "outerringsetting": "最外环设置", "outerquotaringsetting": "外指标环设置", "80percentringsetting": "八分环设置", "30percentringsetting": "三分环设置", "ringouterringsetting": "环外环设置", "dashlinesetting": "虚线环设置", "insiderquotalinesetting": "里指标环设置", "centerpiechartsetting": "中饼图设置", "dashlineamount": "虚线数量", "quota": "指标", "quotaline": "指标线", "quotalineamount": "指标线数量", "quotalinelength": "指标线长度", "quotalinewidth": "指标线宽度", "quotalinecolor": "指标线颜色", "showtickline": "刻度线显示", "circleradius": "半径", "ticklineamount": "刻度线数量", "ticklinelength": "刻度线长度", "ticklinewidth": "刻度线宽度", "ticklinecolor": "刻度线颜色", "100persentcolor": "100%处颜色", "three1Color": "一段颜色", "three2Color": "二段颜色", "three3Color": "三段颜色", "showring": "环显示", "ringcolor": "环颜色", "ringwidth": "环宽度", "showquotaline": "指标线显示", "shadowcolor": "阴影颜色", "shadowblur": "模糊系数", "crossstar": "十字星", "Pentagram": "五角星", "hexagram": "六芒星", "reverse": "翻转", "blocks": "分块", "apple": "苹果", "samsung": "三星", "xiaomi": "小米", "access": "访问", "consult": "咨询", "order": "订单", "click": "点击", "show": "展现", "colorat0": "0处颜色", "colorat0.5": "0.5处颜色", "colorat1": "1处颜色", "groupcolumnchart": "分组柱状图", "columnchart": "柱形图", "groupfield": "分组字段", "linechart": "折线图", "liquidchart": "水波图", "areafill": "区域填充", "stackedareachart": "堆积面积图", "areachart": "面积图", "multilinechart": "多折线图", "pscatter": "散点图", "piechart": "饼图", "classifyfield": "分类字段", "pointchart": "漂浮点", "radarchart": "雷达图", "scatter": "气泡图", "spark": "礼花", "stackedbarchart": "堆叠条形图", "stackedcolumnchart": "堆叠柱状图", "wordcloud": "词云图", "rosechart": "玫瑰图", "bulletchar": "子弹图"}, "particle": {"motion": "粒子运动", "color": "粒子颜色", "opacity": "粒子透明度", "count": "粒子数量", "size": "粒子大小", "autoconnect": "自动连线", "connectdistance": "连线距离", "connectcolor": "连线颜色", "connectopacity": "连线透明度", "connectwidth": "连线宽度"}, "describe": {"itemnamedescribe": "支持itemName*或*itemName两种匹配方式，如果输入*，代表匹配所有对象。", "typedescribe": "在白名单匹配范围内的对象才能被访问，黑名单用于屏蔽白名单中的对象。", "headerdescribe": "表头，可选，为空代表显示使用数据集名称，多个表头用英文逗号分隔", "columndescribe": "字段，可选，为空代表显示所有数据集数据，多个字段用英文逗号分隔", "widthdescribe": "宽度，默认1000", "heightdescribe": "高度，默认400", "showvaluedescribe": "false|true，柱子上是否显示数值，默认true", "tickdescribe": "number，x坐标显示位置不够时，控制间隔点数", "reportisempty": "报表不能为空，请添加报表", "choosetemplate": "请选择模板", "least500": "最小500", "least1000": "最小1000", "templatetip": "帮助：模板需要符合vue模板语法规范，必须指定一个根标签，一般是div，支持所有iView组件。数据源结果以数组的形式返回，根属性为：dataList，访问数据时请直接迭代dataList获取需要的数据。", "configtip": "帮助：配置是一段javascript代码，需要定义包含methods、computed等属性的对象。", "uploadtip": "帮助：只支持上传图片，引用图片路径范例：<img src=\"{file:filename.png}\"/>", "inputtext": "请输入文本或绑定数据源"}, "district": "县级行政区", "firework": "烟花", "numberfield": "数值字段", "basicwidget": "基础组件", "otherwidget": "其他组件", "movetop": "移到顶端", "moveup": "上移一层", "movedown": "下移一层", "movefloor": "移到底端", "reportmanage": "报表管理", "templatemanage": "模板管理", "square": "方形"}, "dashboard": {"dashboard": "仪表板", "dashboardmanage": "仪表板管理", "rangewidth": "环形厚度", "systemdashboard": "系统面板", "personaldashboard": "个人面板", "widgetname": "组件名称", "widgetnameempty": "组件名称不能为空！", "basicsetting": "基础设置", "datafilter": "数据过滤", "formatdisplay": "格式展示", "nohyperlink": "暂无超链接", "conditionisnull": "展示格式存在必选条件为空！", "conditionfail": "有条件不成立！", "sraechconditionempty": "存在为空的搜索条件！", "valuelisthasempty": "存在为空的结果valueList！", "getwidgetfailed": "获取仪表板组件失败", "isdefaultdata": "是默认数据", "barchart": "条形图", "editdashboard": "仪表板编辑", "getwidgetdatafail": "获取仪表板组件数据失败", "deletewidgetconfirm": "确定删除该组件【{target}】？", "target": "标线", "frontend": "前景"}, "deploy": {"triggerstate": "触发状态", "triggerrange": "触发范围", "integratedaction": "集成动作", "appmodule": "应用/模块", "jobtype": "作业类型", "integratedmanagement": "集成管理", "pipelinetype": "流水线类型", "allapplication": "所有应用", "allmodule": "所有模块", "functionoverview": "功能概述", "functiondesc": "指定的应用、模块、环境的发布作业，到达指定状态（如作业执行完成）时，触发调用外部系统接口，例如UAT服务的发布作业完成时，自动触发调用PRD服务接口，触发PRD环境的自动发布作业。", "configmode": "配置方式", "triggerconditionfilter": "触发条件过滤", "triggerconditiondesc": "可指定应用、模块、环境范围，范围内的发布作业才会触发动作；可指定一个或多个作业状态，发布作业流转到指定状态才触发动作", "externalintegrationmode": "外部调用集成方式", "externalintegrationmodedesc": "依赖本平台的【集成管理】功能，调用外部接口，因此需要预先配置【集成管理】，然后在webhook配置页面引用相应的集成配置，配置方法如下：配置入口：在【系统配置】模块下，进入【集成管理】菜单，点击页面左上角的【+配置】按钮添加集成", "integrationconfig": "集成配置：【数据规范】需选择【发布触发器数据规范】，【URL】【请求设置】按照外部接口实际情况填写即可", "owninguser": "所属用户", "groupuser": "同组用户", "otheruser": "其他用户", "copyfile": "复制文件", "movefile": "移动文件", "targetpath": "目标路径", "foldernamerepeat": "存在同名文件夹", "filenamerepeat": "存在同名文件", "packagedownload": "打包下载", "newfolder": "新建文件夹", "uploadthefileandextractit": "上传文件并解压", "copythedownloadpackageaddress": "复制下载包地址", "lastrevisiontime": "最后修改时间", "activeversion": "活动版本", "versionrollback": "由{oldversion}回退到{newversion}", "thelatestcodeproblem": "最新一次代码问题", "latestfifthcommentratestatistics": "最新五次注释率统计(%)", "thelatestfivecodestatistics": "最新五次代码统计", "lastcodetestresult": "最新一次代码测试结果", "lastfiveincrementalcoveragerate": "最新五次增量覆盖率(%)", "lastfivefullcoveragerate": "最新五次全量覆盖率", "codequalitytrend": "bug/漏洞/代码异味趋势图", "coderepeatability": "重复度趋势图", "massvalve": "质量阀", "totalproblem": "总体问题(阻断/严重/主要)", "newaddproblem": "新增问题(阻断/严重/主要)", "bugs": "总体Bugs", "newbugs": "新增Bugs", "newreliabilityrating": "新代码可靠率", "overallvulnerability": "总体漏洞", "debt": "债务", "newdebt": "新增债务", "maintainablerateofnewcode": "新代码可维护率", "overallcodeflavor": "总体代码味道", "overallsecurityhotspot": "总体安全热点", "newcodeodor": "新增代码异味", "newsecurityhotspot": "新增安全热点", "numberofnewlinesofcode": "新增代码行数", "totallinesofcode": "代码总行数", "validlineofcode": "代码有效行", "linecommentrate": "行注释率", "apicommentrate": "API注释率", "ipname": "IP、名称", "appnotsettingenv": "当前应用没有配置环境", "instancename": "实例名", "publishstatus": "发布状态", "lastpublishtime": "最后发布时间", "lastpublisheduser": "最后发布用户", "projectdirectory": "工程目录", "successrate": "成功率[成功/失败]", "fullbranchcoverage": "全量分支覆盖率", "incrementalbranchcoverage": "增量分支覆盖率", "fulllinecoverage": "全量行覆盖率", "incrementalrowcoverage": "增量行覆盖率", "sealplate": "封版", "selectaddversion": "请选中应用或模块后再创建版本", "noconfigauthtip": "您没有当前应用的“编辑配置”权限，请联系管理员授权", "notversionproductauth": "您没有当前应用的“版本&制品管理”权限，请联系管理员授权", "failurecount": "(失败{target}次)", "flowchart": "流程图", "discarddraft": "丢弃草稿", "editdraft": "编辑草稿", "applicationsystem": "应用系统", "nosavedraft": "您有未保存的草稿，请选择继续编辑草稿或丢弃草稿重新编辑", "userhasnosavedraft": "用户{username}/{userid}有未保存的草稿，请选择继续编辑草稿或丢弃草稿重新编辑", "chooseatleastonephase": "至少选择一个阶段", "chooseatleastonescenario": "至少选择一个场景", "phaseatleastonetool": "阶段{target}设置：至少选择一个工具", "phasesetcondition": "阶段{stepname}设置【{operationname}】请输入条件", "phasesetinputparamtip": "阶段{stepname}设置：【{operationname}】输入参数数据填写不完整", "phasesetfreeparamtip": "阶段{stepname}设置：【{operationname}】自由参数数据填写不完整", "phaseselectpresetparamtip": "阶段{stepname}设置：【{operationname}】请选择预置参数集", "phasetooldeletetip": "阶段{stepname}工具【{operationname}】已被删除，请清理该工具后保存", "noappviewauth": "您没有[{abbrname}应用]的配置查看权限", "scenesetting": "场景设置", "selectiontool": "选择工具", "actuator": "执行器", "copyconfig": "复制配置", "deleteenv": "删除环境", "noapplytip": "系统未添加应用，点击", "applynotconfigselect": "当前应用尚未添加配置，请选择", "pipelinetemplate": "流水线模板", "addatleastonescene": "至少添加一个场景", "executivestrategy": "执行策略", "presettarget": "预设目标", "executetargetdesc": "将执行目标按数量等分为N个批次，先后执行，阶段组设置的分批数量优先级高于组合工具或作业中设置的分批数", "deleteallcite": "编辑参数英文名或删除参数时，将解除所有引用关系，是否继续？", "referencequery": "引用查询", "noparamreference": "暂无参数引用", "upstreamanddownstreamparametervalues": "上下游参数值", "upstreamanddownstreamparametersets": "上下游参数集", "presetexecutiontarget": "预设执行目标", "editphasetarget": "编辑阶段：{target}", "viewphasetarget": "查看阶段：{target}", "dragtochangetheorder": "拖拽改变顺序", "applynoaddmoduletip": "当前应用未添加模块，无需设置", "operationauth": "操作权限", "envauth": "环境权限", "scenarioauth": "场景权限", "pleaseselectatleastonepermission": "请至少选择一项权限", "batcheditpermission": "批量编辑权限", "hierarchyswitching": "层级切换", "existdisablephase": "存在禁用阶段", "existheavyloadphase": "存在重载阶段", "deleteallauthconfirm": "确认删除所有人的所有权限？", "deleteallauthconfirmtarget": "确认删除{target}的所有权限？", "batchdeleteselectedauthconfirm": "批量删除被选中权限？", "useofstate": "使用状态", "displaysystemname": "显示系统名", "abbreviation": "简称", "superpipeline": "超级流水线", "joblist": "作业列表", "batchjob": "批量作业", "copyenv": "复制环境", "copystrategy": "复制策略", "copytotheexistingenvironment": "复制到现有环境", "targetenvironment": "目标环境", "notconfigenvdesc": "仅支持将环境配置信息复制到未配置或已清空配置的环境", "adaptationfileadaptation": "适配文件适配", "casedifference": "实例差异", "notconfigfiletip": "未配置配置文件适配，点击", "configfilecasedifference": "配置文件实例差异", "notconfigdifftip": "未配置实例差异，点击", "notaddexampletip": "未添加实例，点击", "dbconfig": "DB配置", "bindaccount": "绑定账号", "databaseschema": "数据库schema", "inputformatisnotstandardpleaseenter": "输入格式不规范，请输入：dbname.username", "thereisoneandonlyonedot": "dbname.username,有且只有一个点号", "schemaformatdesc": "schema输入格式：dbname.username", "customparameter": "自定义参数", "connectiontimeout": "连接超时(秒)", "autocommit": "自动提交", "dbversion": "DB版本", "ignoreerrors": "忽略报错", "dbarole": "DBA角色", "selectexistexample": "选择现有实例", "configurationmodel": "配置模型", "selectexample": "选择实例", "maintenancewindow": "维护窗口", "versionprefix": "版本前缀", "versionprefixdesc": "指定要创建版本的前缀，可不填", "interceptionrule": "截取规则", "versionruledesc": "正则表达式，指定使用分支的哪个部分作为主版本，不给定或正则表达式匹配失败，则使用分支名。合法的正则表达式必须包含成对的(、)，示例：develop(\\d+(\\.\\d+)*\\.\\d+)提取develop1.2.3分支中的1.2.3作为主版本号", "splicingcommitid": "拼接commitID", "splicingcommitiddesc": "在版本号中拼接最近一次提交的 commit ID", "unspliced": "不拼接", "splicing": "拼接", "downloadscript": "下载脚本", "gitdesc": "代码仓库合并代码后（如GitLab的post-receive事件、SVN的post-commit事件），调用本系统接口，创建发布作业，或创建发布批量作业。", "gitlabconfigmode": "GitLab仓库配置方式", "writewarehouseinfo": "填写仓库信息", "writewarehouseinfodesc": "需要填写仓库信息，包括GitLab服务地址、用户名、密码、仓库名、分支信息", "writesvnwarehouseinfodesc": "需要填写仓库信息，包括SVN服务地址、仓库名、分支信息", "actionconfig": "动作配置", "actionconfigdesc": "绑定动作，如创建作业、创建批量作业，并填写动作参数，如发布作业的版本号等", "gitlabcreatehook": "GitLab创建Hook", "gitlabcreatehookdesc": "本系统将根据第1步中的仓库信息，调用GitLab接口，自动创建指定分支范围内，由post-receive事件触发的Hook", "takeeffect": "生效", "takeeffectdesc": "配置完成后，当第1步中指定的仓库及分支发生post-receive事件时，GitLab将通过接口访问本系统，根据第2步中的作业参数，在本系统创建发布作业", "svnwarehouseconfig": "SVN仓库配置方式", "svncreatehook": "SVN创建Hook", "svnwarehousesetting": "SVN仓库，需要用户自行登陆SVN服务器，修改Hook脚本", "enterhooksdirectory": "进入hooks目录", "createpostcommit": "基于post-commit模板文件，创建post-commit文件并作执行授权", "updatepostcommit": "修改post-commit脚本，在脚本中调用本系统创建作业接口(需要修改下方脚本中DEPLOYURL参数的接口地址)", "notautodeploynotcontinue": "如果提交信息中没有 --autodeploy，不执行后续持续集成动作", "formatteddata": "格式化数据", "svntakeeffectdesc": "配置完成后，当第1步中指定的仓库及分支发生post-commit事件时，SVN将执行post-commit脚本，调用本系统接口，根据第2步中的作业参数，在本系统创建发布作业", "postcommitscript": "post-commit脚本.txt", "configurationlist": "配置列表", "warehouse": "仓库", "warehousetype": "仓库类型", "warehousename": "仓库名", "warehouseserveraddress": "仓库服务器地址", "serviceaddress": "服务地址", "branchactiondesc": "分支上发生此事件时触发集成动作", "actiontype": "动作类型", "actiontypedesc": "动作修改之后，动作对应的配置信息将会清空", "delaytime(s)": "延迟时间(s)", "updatewarehousewillbedelete": "如果修改了仓库服务器地址或仓库名称，原有仓库的gitlab webhook将被删除", "branchnamewildcardcharactersaresupported": "分支名称，支持通配符", "branchtriggerintegrationdesc": "此分支上的事件会触发集成，如果不填所有分支都会触发", "branchchangedesc": "仓库下的分支名称，此分支内容的变化将会触发持续集成。如果填“/”，则repo的任何分支变更都会触发持续集成.支持使用通配符", "reponamedesc": "示例：projects/moduleName", "addressexample": "示例：/www/svn/demo_repo", "warehousecompletepath": "仓库名称的完整路径名称，示例：/home/<USER>/svn/repoName", "branchexample": "示例：branches/v1.0.0", "branchfiltersvntooltip1": "1、*：匹配单层目录内任意数量的字符。", "branchfiltersvntooltip2": "如 /Projects/Branches/* 匹配 /Projects/Branches/v1.2.3/，但不匹配 /Projects/Branches/v1.2.3/Java/", "branchfiltersvntooltip3": "2、**：匹配任意数量的字符。", "branchfiltersvntooltip4": "如 /Projects/Branches/** 匹配 /Projects/Branches/v1.2.3/、/Projects/Branches/v1.2.3/Java/ 和 /Projects/Branches/v1.2.3/Java/Spring/", "branchfiltersvntooltip5": "3、?：匹配单个字符。", "branchfiltersvntooltip6": "如 /Projects/Branches/v?.?.? 匹配 /Projects/Branches/v1.2.3/，但不匹配 /Projects/Branches/v1.2.3.4/", "branchfiltersvntooltip7": "4、{pattern1, pattern2,...patternN}：只匹配给定的模式。", "branchfiltersvntooltip8": "如 /Projects/Branches/{v1.2.3, v1.2.4, v1.2.5} 只匹配 /Projects/Branches/ 下的 v1.2.3, v1.2.4, v1.2.5", "branchfiltersvntooltip9": "5、方括号模式指定单个匹配的字符集。", "branchfiltersvntooltip10": "如 [0-9] 匹配单个数字，[a-z, A-Z] 匹配任意一个字母，/Projects/Branches/v[0-9.]* 匹配 /Projects/Branches/v1.2.3/、/Projects/Branches/v1.2.3.4/", "branchfiltersvntooltip11": "6、要匹配特殊字符需用 “\\” 转义，如 /Projects/Branches/v\\[ 匹配 /Projects/Branches/v[/", "commitid": "提交ID", "associatedevent": "关联事件", "notsceneauth": "您没有[{target}]场景权限，请联系管理员授权", "notenvauth": "您没有[{target}]环境权限，请联系管理员授权", "envnotexample": "模块在{target}环境下没有实例", "instancefiltering": "实例筛选", "copymodule": "复制模块", "copytoexistingmodule": "复制到现有模块", "objectmodule": "目标模块", "objectmoduledesc": "仅支持将模块配置信息复制到未配置或已清空配置的模块", "configrunnergroup": "配置Runner组", "runnergroup": "Runner组", "applicationinformation": "应用信息", "moduleinformation": "模块信息", "continuousintegration": "持续集成", "currentoperationwillclear": "当前操作将清理：", "currentpipelineconfig": "1）当前应用流水线配置", "currentappauthconfig": "2）当前应用权限配置", "allmoduleconfig": "3）所有模块配置", "allenvconfig": "4）所有环境配置", "deleteconfirm": "且清理后不支持撤销，需重新配置，是否继续？", "currentmoduledatareset": "1）当前模块的流水线相关的重载数据", "currentmoduleexecuteconfig": "2）当前模块的执行器配置", "currentmoduleallenvconfig": "3）当前模块下所有环境的配置", "moduleenvdeleteconfirm": "且清理后，当前模块及模块的所有环境，均继承应用配置；清理后不支持撤销，是否继续？", "currentenvdatareset": "1）当前环境的流水线的相关重载数据", "currentenvfileconfig": "2）当前环境的配置文件适配数据", "currentenvdbconfig": "3）当前环境的DB配置", "envdeleteconfirm": "且清理后，当前环境配置均继承模块配置；清理后不支持撤销，是否继续？", "configurationfileadaptation": "配置文件适配", "instancelist": "实例列表", "actuatorgroupallocation": "执行器组分配", "modulenotconfigrunnergroup": "当前模块未配置runner组，将导致发布作业无法运行，点击", "actuatorgroup": "执行器组", "associatedrunner": "关联runner", "connectionmode": "连接方式", "commandport": "命令端口", "heartbeatport": "心跳端口", "chooseatleastonetool": "至少选择一个工具", "currentmodulenotadddbconfig": "当前模块未添加DB配置，点击", "compiletime": "编译时间", "addbuildno": "新建BuildNo", "selectbuildno": "选择BuildNo", "sealedplate": "(已封版)", "unsealedversion": "(未封版)", "selectversion": "选择版本", "moduleenvnotinstance": "{modulename}模块在{envname}环境下没有实例", "currentmodulenotsetexecutetip": "当前模块未配置执行器，无法创建作业，", "clickadd": "点击添加", "atleastselectamodule": "模块设置：至少选择一个模块", "moduleconfigselectversion": "模块设置：【{target}】请选择版本", "moduleconfigatleastselectainstance": "模块设置：【{target}】至少选择一个实例", "createsuccess": "创建成功", "createfail": "创建失败：", "appmodulecreatesuccess": "应用模块{target}创建成功，", "clickredirect": "请点击跳转", "applymodule": "应用模块", "currentapplynoconfig": "当前应用尚未配置", "applynoconfigmodule": "当前应用未配置任何模块", "noconfigenv": "未配置环境信息", "cancelwait": "取消等待", "forceunlock": "强制解锁", "lockingoperation": "上锁作业", "lockingduration": "锁定时长", "locktime": "锁定时间", "jobnotexecutefinish": "作业{target}未执行完成，", "jobexecutionmayfailconfirm": "可能导致作业执行失败，是否继续", "addbatchjob": "添加批量作业", "creationmode": "创建方式", "directcreation": "直接创建", "jobauthorization": "作业授权", "authorizeduser": "授权用户", "authorizedusercanexecutebatchjob": "被授权的用户可执行当前批量作业", "rejectbatchjobconfirm": "确认驳回当前批量作业？", "passbatchjobconfirm": "确认通过当前批量作业？", "batchchannel": "批量通道", "tasktimelimitfiveminutes": "为防止任务刚提交就过期，只允许选择5分钟后的时间", "manualtrigger": "人工触发", "automaticexecution": "自动执行", "atleastaddajob": "请至少添加一个作业", "deletebatchjobconfirm": "确认删除当前批量作业？", "automaticallyexecutesubsequentgroups": "自动执行后续组", "automaticalexecutegroupdesc": "当前组下的所有作业执行完毕后，是否自动执行后续组", "batchjobexecutestrategy": "批量作业执行策略", "skipallfinishedchildjob": "跳过所有已完成的子作业", "skipfinishedignorenode": "跳过所有已完成、已忽略的节点", "executeallchildjob": "执行所有子作业", "childjobexecutestrategy": "子作业执行策略", "executeallnode": "执行所有节点", "noselecttarget": "当前暂无可选的{target}", "roundcountvalidate": "分批设置：请选择或输入正确的数字", "jobparamvalid": "作业参数：数据填写不完整或者错误", "noconfigenvauth": "您没有配置“环境权限”", "noconfigscenauth": "您没有配置“场景权限”", "pleaseselectmoduleenvaddjob": "请选中应用或模块后再创建作业", "phaseexistignorenode": "阶段中存在状态为 已忽略 的执行节点", "pleaseselecttimeperiod": "请选择时间段", "executor": "执行人", "selectjob": "选择作业", "jobtemplate": "作业模板", "envscene": "环境（场景）", "sealededition": "（已封版）", "currentapplynotconfig": "当前应用未配置", "pleaseselectapply": "请先选择应用", "atleastaapplymoduleandwriteversion": "至少选择一个应用模块并且填写版本", "basicinfonotwaritecompletevalid": "当前基本信息未填写完整", "notapplyeditconfigauth": "您没有当前应用的编辑配置权限", "notapplyallsceneexecuteauth": "您没有当前应用所有场景的执行权限", "notapplyallenvexecuteauth": "您没有当前应用所有环境的执行权限", "applynotconfigpipeline": "当前应用未配置流水线", "applynotconfigmodule": "当前应用未配置模块", "applynotconfigenv": "当前应用未配置环境", "modulenotconfigenv": "当前模块未配置环境", "codechange": "代码变更", "cveloophole": "cve漏洞", "lastsynchronizationtime": "最后同步时间", "fileaddcount": "文件增加数", "filemodifycount": "文件修改数", "filedeletecount": "文件删除数", "lineaddcount": "代码行增加数量", "linedeletecount": "代码行减少数量", "scenariosteperror": "场景中的所有阶段均为禁用状态。场景至少需存在一个非禁用状态的阶段", "importpipelineconfig": "导入流水线配置", "coverpipeline": "覆盖流水线", "cancelimport": "取消导入", "relateobjectcover": "关联对象覆盖", "overrelateobjectdescription": "以下流水线关联的对象，在系统中已经存在，如需覆盖，请勾选需要覆盖的对象后，点击下方[导入]按钮，完成导入。", "overexistpipelineiscontinue": "已配置流水线，导入流水线将覆盖已有流水线，是否继续？", "issuecount": "需求数", "deletecodeline": "删除代码行", "addcodeline": "新增代码行", "deletefilecount": "删除文件数", "modifyfilecount": "修改文件数", "addfilecount": "新增文件数", "withoutconfigandeditauth": "当前应用尚未添加配置，请联系发布管理员授权配置", "withoutconfigeditauth": "请联系发布管理员授权配置", "envattr": "环境属性", "addenvattrtips": "未配置属性，点击", "noconfigviewauthtip": "您没有当前应用的“查看配置权限”，请联系管理员授权", "pleasechoosedatabase": "请先选择数据库", "currentenvbuildno": "{target}环境BuildNo", "buildnopending": "待构建", "actuatorgrouptag": "执行器组标签", "blueset": "蓝绿设置", "blueSet": "蓝绿", "ismodified": "已修改", "presetrunnergroup": "预设执行器组", "testrunnerdesc": "如果不指定则会通过ip根据网段匹配执行器组自动分配执行器", "jobcount": "发布作业数", "jobrecord": "发布作业记录", "notapplyallexecuteauth": "您没有当前应用的执行权限"}, "inspect": {"alarmprompt": "告警提示", "alarmobjectvalue": "告警对象值", "alarmobject": "告警对象", "ruleid": "规则ID", "rulename": "规则名称", "applicationofrule": "规则所属应用", "sendanemail": "发送邮件", "latestproblemofinspectiontarget": "巡检最新问题{target}", "sendmaillimitdesc": "单次最多给100人发送邮件，超出100的部分不发送邮件", "inputargetnameip": "请输入目标名称、ip", "thresholdrule": "阈值规则", "jobdetail": "作业详情", "monitoringstate": "监控状态", "inspectionjobstatus": "巡检作业状态", "iplist": "IP列表", "addrule": "添加规则", "inheritglobal": "继承全局", "thressholdrulealert": "在下方选中目标应用，以当前应用的个性化阈值规则，覆盖被选中应用的阈值规则。", "selectapp": "至少选择一个应用", "assetthresholdrule": "当前资产阈值规则：", "globalthresholdrule": "全局阈值规则", "inspect": "巡检", "appinspect": "应用巡检", "noconfig": "当前环境未找到关联配置项，请编辑相关配置项，填写【应用环境】信息", "inspecttool": "巡检工具", "inspectmodule": "巡检模块", "envname": "应用环境", "clustername": "所在集群", "datacenter": "数据中心", "selecttargetwarning": "请勾选至少一类巡检目标后执行", "scheduleinspect": "定时巡检", "newproblem": "最新问题", "assetlist": "资产清单", "moduleinspect": "模块巡检", "noenvconfig": "当前模块（或应用）未配置环境信息", "modulenoasset": "当前模块未找到关联资产", "selectenv": "请至少选择一个环境", "appnoasset": "当前应用未找到关联资产", "selectmodule": "请至少选择一个模块", "allenv": "所有环境", "noconfigenv": "未配置环境", "exeount": "启动后已执行", "jobexerecord": "作业执行记录", "scriptmanage": "脚本管理", "urlconfig": "配置URL拨测", "scriptlist": "脚本列表", "expandconfig": "拓展配置", "urlconfigexample": "URL序列检查配置例子", "lookingthroughreport": "您正在浏览历史报告", "historyreport": "历史报告", "batchinspect": "批量巡检", "connectionagreement": "连接协议", "inputmoduletypename": "填写模型类型名称，或者单个主机的IP（名称），不可编辑", "assetinspectconfirm": "确认对资源<b>{target}</b>执行巡检？", "inspecttoolis": "巡检工具为：", "inspectcondition": "对满足以下条件的所有资产批量巡检", "noconfigtool": "资源<b>{target}</b>未配置巡检工具,无法进行巡检。", "clicktoconfig": "点击配置", "inspectexec": "执行巡检", "inputreturn": "换行输入多个路径", "selectedasset": "表格中勾选的资产", "assetfilter": "当前过滤条件下的所有资产", "unselectedasset": "表格中未勾选资产", "clearfile": "清理文件", "configpath": "配置路径", "filelist": "文件列表", "filechangetime": "文件修改时间", "changerecord": "变更记录", "scanhistory": "扫描历史", "latestchangetime": "最近变更时间", "searchplaceholder": "文件名、资产IP、资产名称", "assetip": "资产IP", "assetname": "资产名称", "assettype": "资产类型", "pathconfig": "路径配置", "batchclearfile": "批量清理文件", "clearcofigfilerecord": "清理{target}所有配置文件记录", "exporttargetrule": "导出指标和规则", "targetfilter": "指标过滤", "inspecttoolsetting": "巡检工具设置", "deleteglobalruleconfirm": "删除全局规则，将自动删除应用规则中相同ID的规则，是否继续？", "inspecttoolmanage": "巡检工具管理", "selecttargetatleast": "至少选中一个指标", "cancelselectedtargetconfirm": "取消采集某一指标，将导致包含该指标的阈值规则失效，是否继续？", "inspectresult": "巡检结果", "problemreport": "问题报告", "ruletooltips": "例如：<br/>/dev开头的挂载点的使用率大于90%的规则是：$.MOUNT_POINTS[NAME startswith/dev].USED%>90，属性匹配规则支持操作符号：>,<,>=,<=,==,!=,startswith,endswith<br/><br/>进程CPU使用率超过50%的规则是：$.TOP_CPU_RPOCESSES.CPU_USAGE{$this/$.CPU_LOGIC_CORES}>50，最后的大括号对取到的属性值进行计算后再跟50进行比较，$this代表属性CPU_USAGE的数值", "resourcetypenotsetinspecttooltiptarget": "资源类型{target}未配置巡检工具，无法进行巡检。"}, "pbc": {"importclassidentifier": "导入分类标识", "edittype": "编辑类型", "addtype": "添加类型", "levelidentifier": "层级标识符", "parenttype": "父类型", "datareport": "数据上报", "paramconfig": "参数设置", "dataelementname": "数据元名称", "dataelementtransferid": "数据元传输标识", "firstclassidentifier": "一级分类标识符", "secondclass": "二级分类", "secondclassidentifier": "二级分类标识符", "thirdclass": "三级分类", "thirdclassidentifier": "三级分类标识符", "fourthclass": "四级分类", "fourthclassidentifier": "四级分类标识符", "classidentifier": "分类标识符", "conform": "报送是否符合要求", "organization": "机构", "financialorganizationcode": "金融机构编码", "editorganization": "编辑机构", "addorganization": "添加机构", "codedescribe": "参照中国人民银行发布的《金融机构编码规范》中定义的14位编码", "loginurl": "认证地址", "reporturl": "上报数据地址", "validurl": "申请检核地址", "validresulturl": "查询检核结果地址", "authtype": "认证类型", "clientid": "客户端id", "clientpassword": "客户端密码", "addinterface": "添加接口", "interfaceid": "接口标识", "interfacename": "接口名称", "importinterfacedefine": "导入接口定义", "importintefacewarning": "警告：重新导入接口定义会清除所有属性相关配置，请谨慎操作。", "importinterfacedata": "导入接口数据", "interfacedeleteconfirm": "确认删除接口{target}及其所有属性配置？", "editproperty": "编辑属性", "propertytransferid": "属性传输标识", "propertytransfername": "属性传输名称", "complexid": "复合属性传输标识", "complextransfername": "复合属性传输名称", "propertyaliasdescribe": "导出数据时可使用此字段作为表头名称，如果不设，则使用原来的名称", "valuerange": "值域", "addrelation": "添加关系", "editrelation": "编辑关系", "valueproperty": "值属性", "exportdata": "导出数据", "noorganization": "没有任何机构配置，请先<a href=\"javascript:void(0)\" @click=\"$router.push({ path: '/corporation-manage' })\">添加</a>", "chooseinterface": "请选择接口", "deletedataconfirm": "确认删除选中数据？", "editdata": "编辑数据", "adddata": "添加数据", "adddataitem": "添加数据项", "mappingconfig": "映射配置", "importinterface": "导入接口", "exporttemplate": "导出模板", "restraintcondition": "约束条件", "datatransferid": "采集数据元传输标识", "defaultvaluedesc": "当属性为空时使用此值代替", "convertvaluedesc": "格式：原值:新值，多个配置用逗号分割", "attributemappingsetting": "属性映射设置", "ruletip": "帮助：机构只会导入符合自身规则的配置项，如机构不配置规则，则代表该模型所有配置项都会关联该机构。", "choosemodel": "请先选择模型或视图", "modelattribute": "模型属性", "uploaddataamount": "需要上报<b class=\"text-primary\">{target}</b>条数据", "batchid": "批次ID", "message": "信息", "classifyidentifier": "分类标识", "dataamount": "条数据", "groupid": "分组ID", "initiationmethod": "发起方式", "restartinbreak": "从中断位置重新执行", "restartexecute": "从头开始重新执行", "selectedinterface": "已选接口", "gatherinterface": "采集接口", "editpolicy": "编辑策略", "addpolicy": "添加策略", "timeplan": "时间计划", "relevanceinterface": "关联接口", "chooserelevanceinterface": "请至少选择一个关联接口", "viewexecutionrecord": "查看执行记录", "interfaceamount": "接口数量", "cromexpression": "定时策略", "lastexecutertime": "最近执行时间", "deletepolicyconfirm": "确定删除策略：{target}？", "exepolicyconfirm": "确定执行策略：{target}？", "uniquekeydesc": "系统自动生成唯一标识", "addenum": "添加成员", "editenum": "编辑枚举成员", "addproperty": "添加属性", "propertyname": "属性名称", "complexname": "复合属性名称", "restraint": "约束", "enumitem": "枚举成员", "deletepropertyconfirm": "确定删除属性：{target}？", "selectdataurl": "查询数据处理状态地址", "errorcode": "错误编码", "facilitycategory": "数据元分类标识符", "facilitydescriptor": "数据元设施标识符"}, "rdm": {"project": "项目", "field": "字段定义", "projectstatus": "项目状态", "objectstatus": "对象状态", "projectinfo": "项目信息", "projectsets": "项目设置", "statussets": "状态设置", "objectsets": "对象设置", "customattribute": "自定义属性", "systemattribute": "系统属性", "initstatus": "初始状态", "buildproject": "创建项目", "isstart": "是否开始状态", "isend": "是否结束", "goto": "流转到", "parentnode": "父节点", "request": "需求", "appsets": "应用设置", "attributesetting": "属性设置", "isrequiredwhencreate": "创建时必填", "nosetnolimit": "不设置代表不限制", "requiredattribute": "必填属性", "targetstatus": "可到达状态", "statusrel": "状态关系", "notgoto": "不流转到", "daterange": "日期范围", "datetimerange": "日期时间范围", "inputtype": "输入类型", "deleteprojectdesc": "永久删除此项目，此操作不可恢复，请谨慎操作。", "deletecurrentprojectdesc": "确认删除当前项目？此操作不可恢复，请谨慎操作。", "deletepropertydesc": "确认删除当前属性？注意：属性删除后该属性的值也会被同时删除，且不能恢复。", "projectname": "项目名称", "projecttype": "项目类型", "managerdesc": "项目负责人可以管理项目相关信息", "projectmember": "项目成员", "startenddate": "起止日期", "peojectdesc": "项目说明", "childrequest": "子需求", "nextstatus": "下一状态", "auditlist": "变更历史", "bug": "缺陷", "requestmanage": "需求管理", "disconnect": "断联", "relativerequest": "关联需求", "attrsetting": "字段设置", "taskmanage": "任务管理", "iterationmanage": "迭代管理", "bugmanage": "缺陷管理", "iteration": "迭代", "isopened": "已开启", "isclosed": "已关闭", "startdate": "预计开始", "enddate": "预计结束", "testcase": "测试用例", "templatesetting": "模板设置", "testcondition": "前置条件", "teststep": "用例步骤", "testresult": "预期结果", "pleasesetstatus": "请先定义状态", "listview": "列表视图", "levelview": "层级视图", "changetime": "变更时间", "editor": "变更人", "changetype": "变更方式", "changeattr": "变更属性", "beforechange": "变更前", "afterchange": "变更后", "noiteration": "暂无迭代", "selectedphase": "已选阶段", "stopsecret": "停用Secret", "enablesecret": "启用Secret", "pleasecreatewebhookurl": "请生成WebhookUrl", "copycommitword": "复制源码关键字", "gitlabcommit": "Gitlab提交", "copyissueid": "复制id", "projectmanager": "项目负责人", "new": "新", "pleaseselectrole": "请勾选需要删除的用户角色", "modulelist": "模块列表", "projectisdeleted": "当前项目已删除", "errortip": "异常提示", "noauthforissue": "您不是当前项目成员，没有访问当前项目的权限。", "apply": "申请", "joinproject": "加入此项目。", "dburl": "数据库链接", "dragapp": "拖动已激活应用进行排序", "saveastemplate": "另存为模板", "saveprojectastemplate": "把当前的项目配置保存为项目模板。", "noauthforeditproject": "您不是当前项目的管理员或所有人，不能对项目进行编辑", "endproject": "结束项目", "closeprojectdesc": "结束的项目只能由管理员重新打开", "projecttemplate": "项目模板", "isclose": "是否关闭", "openproject": "打开项目", "applist": "应用列表", "relativetestcase": "关联用例", "repository": "版本库", "gantt": "甘特图", "noconfig": "此组件无需配置", "cost": "花费", "costdate": "花费日期", "costtime": "花费工时", "usedtimecost": "已用工时", "editstartenddate": "编辑计划时间", "plantimecost": "预估工时", "exceedtimecost": "超出工时", "used": "已用", "exceed": "超出", "testplan": "测试计划", "statusreluser": "状态流转时系统自动设置处理人", "innerparam": "内部变量", "storywall": "故事墙", "forbiddentransfer": "不可流转", "importtestcase": "导入测试用例", "downloadtemplate": "点击下载导入模板", "pleaseactiveapp": "请至少激活一个应用"}, "plugin": {"65535": "0至65535之间的整数", "stepscount": "第 %0 步，共 %1 步", "aquamarine": "海蓝色", "blockquote": "块引用", "breaktext": "文字断行", "bulletedlist": "项目符号列表", "captionforimg": "图片说明：", "captionforimgtarget": "图片说明： %0", "centeredpic": "图片居中", "changeimgtotext": "更改图片替换文本", "titletype": "标题类型", "decreaseindent": "减少缩进", "deletecolumn": "删除本列", "deleterow": "删除本行", "dimgrey": "暗灰色", "downloadable": "可下载", "dropdowntoolbar": "下拉工具栏", "editblock": "编辑框", "editlink": "修改链接", "contenttoolbar": "编辑器块内容工具栏", "contextualtoolbar": "编辑器上下文工具栏", "editingarea": "编辑器编辑区域： %0", "editortoolbar": "编辑器工具栏", "fullsizeimg": "全尺寸图片", "headercolumn": "标题列", "headerrow": "标题行", "headertarget": "标题{target}", "imgtoolbar": "图片工具栏", "imgwidget": "图片组件", "inline": "行内", "increaseindent": "增加缩进", "insertcolumnleft": "左侧插入列", "insertcolumnright": "右侧插入列", "insertimg": "插入图像", "insertimgorfile": "插入图片或文件", "insertmedia": "插入媒体", "insertparagraphafter": "在后面插入段落", "insertparagraphbefore": "在前面插入段落", "insertrowabove": "在上面插入一行", "insertrowbelow": "在下面插入一行", "inserttable": "插入表格", "leftalignedimg": "图片左侧对齐", "lightblue": "浅蓝色", "lightgreen": "浅绿色", "lightgrey": "浅灰色", "linkurl": "链接网址", "mediaurl": "媒体URL", "mediawidget": "媒体小部件", "mergecelldown": "向下合并单元格", "mergecellleft": "向左合并单元格", "mergecellright": "向右合并单元格", "mergecellup": "向上合并单元格", "mergecell": "合并单元格", "numberlist": "项目编号列表", "openfilemanager": "打开文件管理器", "openinnewtab": "在新标签页中打开", "openlinkinnewtab": "在新标签页中打开链接", "openmediainnewtab": "在新标签页打开媒体", "pastemediaurl": "在输入中粘贴媒体URL", "richtexteditor": "富文本编辑器", "rightalignedimg": "图片右侧对齐", "selectcolumn": "选择列", "selectrow": "选择行", "showmore": "显示更多", "sideimg": "图片侧边显示", "splitcellhori": "横向拆分单元格", "splitecellverti": "纵向拆分单元格", "tabletoolbar": "表格工具栏", "textalternative": "替换文本", "turquoise": "青色", "unlink": "取消超链接", "uploading": "正在上传", "widgettoolbar": "小部件工具栏", "wraptext": "文字环绕", "correcttime": "正确的时间", "intgreaterthan0": "大于等于0的整数", "floatpointnumber": "浮点数", "smtpserver": "SMTP服务器地址", "popserver": "POP服务器地址", "imapserver": "IMAP服务器地址", "ipadressandport": "IP地址和端口", "positivenumberor-1": "-1或正数", "adress": "保留地址", "mask": "掩码", "nonnegativeint": "非负整数", "int": "整数", "alphabetornumber": "英文字母或数字", "alphabetnumberempty": "英文字母或数字,允许空格", "capalphabet": "英文大写字母", "loweralphabet": "英文小写字母", "1to12": "1至12之间的整数", "properlyformattestring": "正确格式的字符串"}, "codehub": {"recentlyupdate": "最新更新", "proxyaddress": "代理地址", "proxyaddressdesc": "与代码库服务部署在同一台机器上，用于创建SVN仓库", "agentusername": "代理用户名", "agentpassword": "代理密码", "copyurladdress": "复制URL地址", "copyworkingcopyroute": "复制workingcopy路径", "warehouseservices": "仓库服务", "associatedsystem": "关联系统", "associatedsubsystems": "关联子系统", "branchpath": "分支路径", "tagpath": "tag路径", "warehousesynchronization": "仓库同步", "branchtobesynchronized": "待同步分支", "syncexecuteandback": "已后台执行仓库同步", "typename": "类型名称", "repositorylist": "仓库列表", "vouchertype": "凭证类型", "pleaserepositoryservice": "请先选择仓库服务", "branchmerge": "按分支合并", "issuemerge": "按需求合并", "createmergerequest": "创建MR", "strategytype": "策略类型", "issuesstatus": "需求状态", "fixedsourcebranch": "固定源分支", "versionstrategy": "版本策略", "strategyname": "策略名称", "accessaddress": "访问地址", "apidescrition": "接口描述", "noapihelpinfo": "暂无接口帮助信息", "speciallytreated": "特殊处理的", "syncissues": "同步需求", "issuessource": "需求来源", "issuesnumber": "需求编号", "actionname": "动作名称", "triggersystem": "触发系统", "triggersubsystem": "触发子系统", "triggerversion": "触发版本", "targetbranchdesc": "用英文逗号分隔输入多个分支，支持通配符匹配", "triggerrecord": "触发记录", "originalmr": "原始MR", "actiontriggertime": "动作触发时间", "actionexecuteresult": "动作执行结果", "helpdesc": "帮助说明：点击变量名即可复制变量，变量在动作被触发时替换为实际值，支持Freemarker模板引擎的语法。", "selectoriginbranchandtargetbranch": "请选择源分支和目标分支", "unknownrequirement": "未知需求", "effectivedemand": "有效需求", "invaliddemand": "无效需求", "mergerequestdesc": "MR描述", "issuesvalid": "需求有效性", "retrievesubmissionlogs": "检索提交日志", "addmergeissues": "加入待合并需求", "inputissuesnumberdesc": "手工输入需求号，逗号分隔", "issueslogmaxcount": "检索提交日志条数不能小于1", "pleaseselectanotificationtemplate": "请选择通知模板", "pleaseselectthenotificationobject": "请选择通知对象", "pleaseselecttemplateandreceiver": "请选择模板和收件人", "pleaseselectanaction": "请选择动作", "exceptionnotification": "异常通知", "pleaseenterthetriggerpointname": "请输入触发点名称", "policylist": "策略列表", "confirmclearconfiguration": "确定清空配置", "confirmdeleteconfig": "确定删除该配置：动作", "pleaseselectaproject": "请选择项目", "temporarilyunavailablecreatemr": "暂时无法创建MR", "pleaseselectatleastonerequirement": "请至少选择一个需求", "mergertype": "MR类型", "versionnobuildnewmerger": "该版本无法创建新的MR", "chooseastrategtfromtable": "请从表格里勾选一个策略", "actionsubject": "操作对象", "triggerlog": "查看触发记录", "mergenumber": "MR编号", "allrequirements": "所有需求", "conflictingneeds": "冲突需求", "mergedrequirements": "已合并需求", "triggerappmodule": "触发模块", "codereview": "代码评审", "copycurrenturl": "复制当前URL", "thereiscurrentlynoerrormessage": "暂无错误信息", "copycurrentid": "复制当前id", "linebylinecomparison": "逐行对比", "leftandrightcomparison": "左右对比", "postcomments": "发布评论", "encounteringconflictingneeds": "遇到冲突需求", "mergerequeststatus": "合并状态", "consolidationresult": "合并结果", "postmergerequestcomments": "发表了MR评论", "changestofiletarget": "对文件{target}的变更", "commentedonaline": "发表了行评论", "addconfig": "编辑配置", "editconfig": "添加配置", "branchname": "分支名", "editbranch": "编辑分支", "addbranch": "新增分支", "confirmactiondatamessage": "此动作会导致数据丢失。为防止有意外操作，所以要求额外的操作来确认你的实际意图。请输入分支名 <span class='text-danger'>{target}</span> 继续操作或关闭本对话框取消操作。", "confirmdeletebranch": "确认删除分支", "branchnotsame": "分支名称不匹配", "confirmdeletetag": "是否确认删除该标签", "tagorbranch": "标签或分支", "finalcommit": "最后提交：", "backparent": "返回上层", "nocommitfile": "此仓库尚未执行过代码提交,暂无文件", "edittag": "编辑标签", "addtag": "新增标签", "tagname": "标签名", "nogroupmembers": "暂无分组成员", "exportexcel": "导出Excel", "userauth": "用户授权", "groupauth": "分组授权", "delauth": "是否确认删除该权限", "expiresat": "截止日期", "usergourpname": "用户名/组名", "readwrite": "读写", "notauth": "无权限", "exporting": "导出中", "editauth": "编辑授权", "addauth": "添加授权", "group": "组", "protectbranch": "保护分支", "delprotectbranch": "是否确认删除该保护分支", "addprotectbranch": "添加分支保护", "selectresource": "已选资源", "commitcountinfo": "当前已加载 {allcount} 条，找到满足条件的提交 {currentcount} 条", "choosebranchortag": "请选择分支或标签", "commituserorlogkeyword": "提交人或提交日志关键字", "packupcontent": "收起对比内容", "continuesearch": "继续查找", "loadmore": "加载更多", "copyfileurl": "复制文件路径", "copyfilename": "复制文件名字", "clicktoexpandmore": "点击展开更多", "thisdifferencehasbeenfolded": "此差异已折叠", "clicktoexpandthedetails": "点击展开详情", "binaryfilecannotviewcontent": "此文件为二进制文件，暂不支持在线查看具体内容，", "clickheretodownloadthefile": "点此可以下载文件", "nochangesmade": "没有变更内容", "thefileisempty": "该文件为空", "issuedetail": "需求详情", "filedeleted": "文件已被删除", "defaultbranch": "默认分支", "mainbranch": "主干分支", "rowadd": "行添加", "rowdelete": "行删除", "filechangenumber": "个文件变更", "handlemergerequest": "处理MR", "revokerequirement": "撤销需求", "selectoneresource": "请至少选择一个资源", "noaddauthresource": "暂无可添加权限的资源", "backmergerequest": "返回MR", "branchproject": "分支保护", "filenameadjusttotarget": "文件名由{fromfileName}修改为{tofileName}", "binaryfilenosupportview": "此文件为二进制文件，暂不支持查看"}, "documentonline": {"currentpagesearch": "当前页面相关帮助", "openhelp": "打开帮助中心", "openhelpdocument": "在新页面打开帮助", "searchhelp": "搜索帮助", "helpcenter": "帮助中心", "whathelp": "请问有什么能帮到您？", "problemdes": "问题描述", "relcurrclass": "关联当前分类", "unlassifieddoc": "未分类文档", "representmenupage": "表示菜单页面", "representsubpages": "表示子页面"}, "dr": {"assetconfig": "资产配置", "scenarioconfig": "场景配置", "centername": "中心名称", "basicservices": "基础服务", "associationmodel": "关联模型", "network": "网络", "migrationdirection": "迁移方向", "datareferencetip": "当前数据被场景引用，无法删除", "organizationalstructure": "组织架构", "affiliatedorganization": "所属组织", "currentoranghaschildnodelete": "当前组织架构已有子级不可删除", "currentorangbindusernodelete": "当前组织架构已绑定用户不可删除", "selectatleastmodel": "至少选择一个模型", "applicationtypename": "应用类型", "org": "恢复机构", "fileconfigpath": "配置文件路径", "datacenterrel": "关联数据中心", "selectdatacenter": "至少选择两个数据中心", "publicapplication": "公共服务", "combopname": "自动化编排", "dependency": "依赖", "dependencyservice": "依赖服务", "scencedependency": "场景依赖", "servicedependency": "服务依赖", "scenarioplan": "场景预案", "relyonmyservices": "依赖我的服务", "servicerelyon": "我依赖的服务", "dependentapp": "依赖应用", "preparation": "预案", "exerciseplan": "演练计划", "dependencyscene": "依赖场景", "sourcedatacenter": "来源数据中心", "targetdatacenter": "目标数据中心"}, "diagram": {"widgetlist": "图元列表", "widget": "图元", "lane": "泳道", "widgetmanage": "图元管理", "dragtosort": "拖动改变排序", "pleaseselectwidget": "请至少选择一个图元", "commoncatalog": "普通目录", "cmdbcatalog": "配置项目录", "catalogitem": "目录项", "childitem": "子节点", "relpath": "关系路径", "graph": "架构图", "catalogtemplate": "当前目录可以使用的模板", "ispublished": "已发布", "tofront": "移到最顶端", "toend": "移到最底端", "publishversion": "发布版本", "confirmcheckversion": "是否确认将当前版本提交审核？", "confirmandpublish": "通过并发布", "line": "直线", "orth": "正交", "startci": "起始模型", "endci": "终点模型", "autofill": "自动填充", "beginci": "起点模型", "snapshotedit": "生成快照&编辑", "startciseted": "已设置起点模型", "allowlinkout": "允许连出", "allowlinkin": "允许连入", "allowresize": "允许改变大小", "allowdelete": "允许删除", "allowmove": "允许移动", "nocientityname": "无显示名", "allowselect": "允许选中", "releaselock": "解除锁定", "tryfill": "模拟填充", "privatecatalog": "个人目录", "publiccatalog": "公共目录", "releaseconfirm": "是否确认发布当前架构图？", "edgemode": "连线模式", "sourcemarker": "起点箭头", "targetmarker": "终点箭头", "classic": "经典", "block": "块", "cross": "交叉", "larger": "放大", "smaller": "缩小", "importtemplate": "导入模板", "strictmode": "严格框选模式", "changestatusconfirm": "是否确认将当前版本状态修改成：{target}？", "editingversion": "编辑版本", "createuser": "创建人", "cientityinfo": "配置项信息", "changedata": "变更数据", "diagramcatalog": "架构图目录", "changearchitecture": "架构变更", "changelist": "变更清单", "isactive": "是否发布", "nodiagramchange": "暂无架构变更", "iscurrentversion": "是否当前版本", "exportsvg": "导出SVG", "exportpng": "导出PNG", "nootherversion": "暂无其他版本", "otherversion": "其他版本", "deleteversionconfirm": "是否确认删除版本：{target}？", "metro": "地铁避让", "manhattan": "正交避让", "dynamicwidget": "动态图元", "startcientity": "起点配置项", "onlyhasdiagram": "只看有图", "nextstatus": "流转状态", "editing": "编辑中", "activing": "激活中", "source": "来源限制", "waitfordo": "待处理", "torel": "下游关系", "fromrel": "上游关系", "rel": "关联关系", "reltypeconfig": "连线类型配置", "edgetype": "线段"}, "license": {"isbanmodule": "到期禁用模块", "graceperiod": "超时宽限天数"}, "alert": {"alerttype": "告警类型", "alert": "告警", "isnormalattr": "作为普通属性展示", "closesuccess": "关闭成功", "istop": "是否置顶", "onlysearchparentalert": "只搜索父告警", "searchallalert": "搜索所有告警", "closeselectedalert": "关闭选中告警", "openselectedalert": "打开选中告警", "deletematchalert": "删除匹配告警", "allalert": "所有告警", "alerttopo": "告警拓扑", "alertdetail": "告警详情", "applychildalert": "同时应用子告警", "transferworker": "转交处理人", "transferteam": "转交处理组", "reportdata": "上报数据", "eventaudit": "事件记录", "alertnotexists": "告警不存在或已被删除", "childalert": "子告警", "dealresult": "处理结果", "createalert": "创建告警", "defaultstatus": "默认状态", "joinalert": "归并到告警", "uniquekey": "唯一键", "alertsign": "告警特征"}}