{"expression": {"eq": "equal to", "ne": "not equal to", "like": "contains", "notlike": "does not contain", "empty": "is empty", "notempty": "not empty", "between": "Within the interval"}, "cmdb": {"cicount": "Number of models", "isshowintopo": "Whether to show in topology", "showintopo": "Show in Topology", "keylevel": "key level", "levelnumber": "{level}", "showrel": "Show Relation", "extendlevel": "Expand Level", "affectcientity": "Affect Config Item", "selectcientity": "Select Config Item", "selectedcientity": "Selected configuration item", "attributelist": "Attribute List", "relpathci": "Please select the models that the path must contain", "relpathcount": "Total {count} relation paths", "relpathcounting": "Counting relation paths...", "belongci": "belonging model", "customviewconfig": "Custom View Configuration", "relpathlist": "Relation Path List", "fieldname": "field name", "fieldtype": "field type", "displaytype": "DisplayType", "relationgroup": "Relationship Group", "legalcheckconfig": "Compliance Check Settings", "newrule": "New Rule", "rulegroup": "Rule group", "chooseci": "<PERSON><PERSON> Model", "citype": "Model Type", "onlyabstractci": "Only look at abstract models", "onlyvirtualci": "Virtual models only", "parentci": "Parent model", "cifile": "Model file", "cimanage": "Management Model", "addci": "Add Model", "editcientity": "Edit configuration item", "deletecientity": "Delete configuration item", "fromcientity": "Source configuration item", "recovercientity": "Recover config item", "cascaderelation": "Cascaderelation", "transactionmanage": "Manage Transactions", "viewpassword": "View password field", "calccascaderelation": "Calculation Cascade Relation", "invokecount": "Number of associations", "upsideuniquekey": "Upside uniquekey", "downsideuniquekey": "Downside uniquekey", "cientity": "Configuration Item", "targetci": "Target model", "upsidename": "Upside Name", "downsidename": "Downside name", "centityname": "Configuration Item Name", "nonamecientity": "Noname config item", "newcientity": "New config item", "uniquerule": "Unique Rule", "manualinput": "Manual input", "upside": "Upside", "downside": "Downside", "ci": "Model", "transactionid": "Transaction ID", "virtualci": "Virtual Model", "abstractci": "Abstract model", "configfile": "Configuration file", "activedate": "Active date", "keymodel": "Key Model", "view": "View", "legalcheck": "Compliance Check", "viewcientity": "View Configuration Items", "hidetopo": "Hide topology", "showtopo": "Show topology", "viewname": "View Name", "alertlevel": "Alert level", "customview": "Custom View", "basicattribute": "Basic Attribute", "allattribute": "All attributes", "nameattribute": "Name attribute", "defaultattribute": "Default attribute", "group": "Group", "history": "History", "uncommittransaction": "Uncommitted transaction", "radius": "Rounded corners", "transaction": "Transaction", "importci": "Import model", "exportci": "Export model", "extendto": "Inherited from", "editci": "Edit model", "cientitylist": "Configuration Item List", "needexportcientity": "Please select the attributes that need to be exported", "invokelist": "Invoke View List", "topo": {"dot": "Layer Layout", "circo": "Circular layout", "neato": "Tension Layout", "osage": "Array layout", "fdp": "Undirected Layout", "twopi": "Star layout"}, "fulljoin": "Full join", "leftjoin": "Left join", "rightjoin": "Right join", "setstartmodel": "Is Start Model?", "innerproperty": "Inner Property", "taginvoked": "The current tag has already been quoted", "viewsetting": "View Configuration", "rebuildallview": "Rebuild all views", "resourcetarget": "Resource Object", "sceneview": "Scene View", "collectiondata": "Collection Data", "discoveryrule": "Discovery Rule", "unknowndevice": "Unknown Device", "targetoid": "Target oid", "matchrule": "Matching Rule", "objectcategory": "Object category", "objecttype": "Object type", "model": "Model", "objectname": "Object name", "centityattribute": "Configuration Item Attribute", "dataview": "Data View", "topoview": "Topology View", "transactionauditperiod": "Transaction Retention Period", "transactiongroupid": "Transaction group id", "invokeasset": "Related assets", "exceptiontemplate": "Exception Template", "componentlist": "Internal Component List", "innerattributelist": "Internal Attribute List", "datasaving": "Saving data", "appnamemodulename": "Application name, module name", "exportselectedassets": "Export selected assets", "exportallfiterconditionassets": "Export all assets that meet the current filtering criteria", "publicprivateaccountchooseoneaccountdesc": "The set of public and private accounts cannot be empty. Please fill in at least one account", "successfullyboundaccountforassetstarget": "Successfully bound account for {target} assets", "failedtobindaccountforassetstarget": "{target}Asset binding account failed", "onlyexportselected": "Export only selected data", "driverci": "Drive Model", "addcientity": "Add Configuration Item", "resourcetypetreenosettingdesc": "Asset list data sourced from Science_ Ipobject_ The detail view was not successfully set in the view settings, and the asset list function is not available", "gotoresourceentitymanagepage": "Click here to set this view", "resourcetypetreesettingdesc": "Select a model as the root directory for the asset manifest classification", "mainci": "Main model", "inherentfield": "Inherent field", "attrci": "Model where the field is located", "inspectime": "Inspection time", "monitortime": "Monitoring time", "modelkey": "Model identification", "modelname": "Model Name", "toattr": "Mapping Properties", "fromci": "Upstream model", "toci": "Downstream model", "privatedataview": "Personal Data View", "publicsceneview": "Global Topology View", "publicdataview": "Global Data View", "changememo": "Reason for change", "globalattr": "Global Properties", "pleaseselectlayout": "Please select a layout", "gotoresourcetypetreesetting": "Please set the root directory in Configuration Management - Asset List", "resourcebatchsearchtooltip": "Batch search is a keyword fuzzy search, in which the wildcard * is supported for batch search of IP, matching any character, such as ***********, ************, and *************", "onlybackbone": "Show only backbone relationships", "showallrel": "Show all branch relationships", "modemapping": "Model Mapping", "ciconfignumber": "Number of configuration items", "relaction": "Relationship strategy", "attrmapping": "Attribute Mapping", "addonedata": "Add a piece of data", "addmoredata": "Add multiple pieces of data", "addonerel": "Add a single relationship", "addmorerel": "Add multiple relationships", "rerunsteptosync": "Fallback <PERSON>", "rerunsteptosynctip": "After the process is rolled back, when it flows back to the current step, resynchronize", "onlyupward": "Just look upstream", "onlydownward": "Just look downstream", "noextend": "Hide Extend Relationships", "nochange": "No modifications made", "cilevel": "Model hierarchy", "cidirectory": "Model Catalog", "directoryname": "Catalog Name", "noaddcidirectory": "No model directory added,", "currentcatalogcitenodeletedescrition": "The current directory node has been referenced. Please remove the reference and delete it", "autoexec": "Automated operations", "abstractciallowextend": "Inherited models can only be abstract models", "asyncpolicy": "Synchronization strategy", "globaleditmodetip": "Global mode, do not delete attribute representatives", "partialeditmodetip": "Local mode, do not change if attribute is not given", "treedraginfo": "Dragging a model can change the order and affiliation of the model, and cannot be dragged when in search mode", "startci": "Select the starting model first", "writeci": "Write model", "writecitip": "Select constants or specify the model for data writing through form mapping. If the parent model is an abstract model and does not support adding configuration items, data needs to be written into the child model.", "subci": "Submodel", "selectrelpath": "Select relationship path", "relciconfigtip": "After configuring the parent model, child models cannot be configured; After configuring the sub model, the parent model cannot be configured, but other sub models can be configured", "topotemplate": "Topology template", "selectagain": "Re select", "topotype": "Unfolding method", "grouptypedescreadonly": "Read only: View configuration items, which can be used as the execution target for the query class combination tool in automation.", "grouptypedescmaintain": "Maintenance: Add modifications to configuration items based on read-only permissions.", "grouptypedescautoexec": "Automated operations: On the basis of read-only permissions, add execution targets that can be used as a combination tool for operations in automation", "tooltipscope": "Only effective on configuration item list, editing, and detail pages", "dataconversionsetting": "Conversion Configuration", "appendrel": "Append relationship", "replacerel": "Replace Relationship", "relationaltable": "Associated Table", "relationalcondition": "Associated conditions", "relationalpretable": "Associate with upper level table", "searchable": "Allow search", "pushmqhelp": "Publish this property to MQ when configuration items change", "searchablehelp": "Allowed search attributes can be used as filter conditions in advanced searches or connected to other model's searchable attributes in custom data views", "topicci": "Subscription model", "condition": "Comprehensive conditions", "automatch": "Automatic matching", "cancelautomatch": "Revoke automatic matching", "collection": "aggregate", "parentattr": "Parent attribute", "initiativecollect": "Active collection", "passivitycollect": "Passive collection", "selectedattr": "optional attribute ", "scripthelp": "The conversion script syntax only support ES5. Click on the optional attribute to copy the expression that obtains the attribute value. Assign the new value to the variable $value to complete the conversion", "expression": "expression", "transformscript": "Conversion script", "relmapping": "Relationship mapping", "globalattrmapping": "Global attribute mapping", "matchfield": "Matching Fields", "supportmultimodelfile": "Help: Supports importing multiple model files simultaneously", "invokeattr": "Reference Properties", "cientityid": "Configuration item ID", "cientityicon": "Configuration item icon", "editablefield": "Editable attributes", "deletecitip": "The current configuration item has been deleted", "expandbyscene": "Expand by scene", "expandbylevel": "Expand by layer", "deleteattr": "Delete attribute", "deleteglobalattr": "Delete Global Properties", "groupbyci": "Group by model", "showrellabel": "Display relationship type", "exportformodel": "Export as Model", "exportforexcel": "Export to Excel", "isterm": "Is it a keyword", "batchimport": "Batch upload", "errordetail": "Exception Details", "collect": "collection", "pause": "stop it", "cientityisnotfound": "Configuration item does not exist", "testaccount": "test result", "repeataccount": "The same protocol and username cannot bind multiple accounts with the same protocol and username to the same asset.", "datadetail": "Data Details"}, "process": {"workordercenter": "Work Center", "catalog": "Services", "catalogmanage": "Service Catalog", "cataloglist": "Catalog List", "task": "Work Order", "flow": "flow", "formlist": "Form List", "relprocess": "Association Process", "scoringdimension": "Dimension", "subtaskpolicy": "Subtask Policy", "policy": "Policy", "submittask": "Apply Request", "viewflowchart": "View flow chart", "relcatalog": "Associated Services", "catalogname": "Service Name", "report": "Report", "reportcontent": "Report content", "reportcatalog": "Report Service", "usernumber": "job number", "focususer": "Follow people", "planStartEndTime": "Plan Start and End Time", "changeowner": "Change Manager", "changecontent": "Description", "changeparams": "Parameters", "startTimeWindow": "Time Window", "changedetail": "Change Details", "unlimitednum": "Unlimited", "anypersoncompletes": "Anyone completes", "allpersoncompletes": "Everyone completes", "dealwithusernum": "Limit of workers", "completepolicy": "Completion Policy", "dealwithusernumdesc": "Under this policy, the number of people to be selected for new subtasks", "btntext": "Text", "custombtn": "Buttons", "solution": "Solution", "solutioncontent": "Solution Content", "relclassify": "Association Classify", "relclassifydes": "When the parent category is selected, it will automatically penetrate downwards, that is, all subcategories under the parent category will be automatically associated with the solution.", "subclassify": "Child Type", "relsolution": "Relsolution", "eventtype": "Event Type", "noderules": "Node Rules", "resetflowtip": "After reset, it will restore to the last saved configuration, do you want to discard the current modification?", "workflow": "Workflow", "showpriority": "Enable priority", "defaultpriority": "Default Priority", "serwindow": "Work schedule", "sertype": "Type", "reporauth": "Assigned to", "tranferreport": "Tranferreport", "allowtranferreport": "Allow transfer", "transfersettings": "Transfer Settings", "transfersettingstip": "The transfer optional relationship type is determined by the above service type", "useoriginalreporter": "Use original reporter", "serallowforwarding": "Services that allow forwarding", "authorizeduser": "authorized user", "worknumper": "Prefix", "worknumrules": "Pattern", "updateworknum": "Update work order number", "nodename": "node name", "hideparams": "Hide matched parameters", "showparams": "Show matched parameters", "btnmapping": "Button Mapping", "statusmapping": "statusmapping", "othersmapping": "Other Mapping", "changetemp": "Change Template", "soptemp": "SOP template", "changegrouptip": "Supports associated user groups, when creating a change template, you can quickly filter the change template through this group", "changetypetip": "Supports custom add types, which can be used to quickly filter change templates when creating changes", "templatesteps": "Steps", "soptip": "Please drag the SOP template from the right to here", "tempnotreferenced": "The template is not active and cannot be referenced", "tempdelete": "The template is referenced and cannot be deleted", "mappingmethod": "MappingMethod", "paramstip": "Prompt: After modifying a variable, it will be replaced synchronously with the corresponding variable referenced in all change steps", "paramsdeltip": "It cannot be restored after deletion, and will synchronously clear the current variables referenced in the following steps.", "stepname": "step name", "step": "step", "stepuser": "Step Processor", "stepinfor": "Step Information", "stepstatus": "Step Status", "batchedituser": "Batch edit handler", "planstartdate": "Plan start date", "dealwithuser": "Dealwithuser", "paramlist": "Variable List", "encoded": "encoded", "changestep": "Change step", "currentstep": "current step", "archivedtype": "Archive Type", "actualstarttime": "actual start", "actualendtime": "actual endtime", "mycreated": "Mine", "savedraftflow": "Saved draft flow: last 6", "autosaveinterval": "Autosave interval: 30s", "restoredrafts": "Restore files from drafts", "autosavedatetime": "Autosave date/time", "importjob": "Import Jobs", "downloadtemp": "Download Template", "importjobtip": "To avoid import failure, please use the template downloaded from this page", "importjobrule": "Support importing .xlsx files within 10MB", "reportstatus": "Status", "recordid": "Record ID", "servicechannel": "Service Channel", "importtime": "Import time", "pleasewait": "The work order is in circulation, please wait...", "relatedknowledge": "Related Knowledge", "sourcename": "Report Channel", "proxy": "Proxy", "expiretime": "Expire time point", "deadline": "Deadline", "transferto": "Transfer to", "nottransfer": "Not transfer", "alwaystransfer": "Direct transfer", "act": "generation", "pendinguser": "Pending user", "changestatus": "Change Status", "timeout": "Timeout", "repeatedevent": "Repeated Event", "steplog": "Step Log", "reltask": "Associated Work Order", "subtask": "Subtask", "formpriorityrule": "The form linkage rule matches multiple priorities, please modify the form or contact the administrator", "autostart": "Autostart", "autostarttip": "Only valid when the handler is unique", "timedout": "Timed out", "distancetimeout": "Distance timeout", "notfocustask": "Unfollow", "focustask": "Focus on tickets", "mytodo": "My todo", "pleaseinputtaskcontent": "Title, work order number or keyword of report content", "formfilter": "Form Filter", "attrdragtip": "Selected attributes (drag to modify order)", "workordercentertypedes": "The category name when displayed as the left menu, if not set, it will be classified into the default category: work order center", "workordertypenumdes": "Whether to display the total number of work order categories on the left, the default is no", "converttoknowdoc": "Convert to knowledge document", "pleselectprioritystep": "Select the priority step to be processed", "selectaskuser": "Specify {target} handler", "changesuspension": "Change Suspension", "circulation": "circulation", "fallback": "fallback", "linename": "line name", "successcirculation": "Confirm success and transfer", "failedcirculation": "Confirmation failed and circulated", "callrecord": "Call Record", "callbackrecord": "Callback Record", "autoexeccompleteerror": "The automated job is being executed and cannot be executed manually", "assigntitle": "Assign step handler", "targettime": "Expected completion time", "processsteplist": "Step List", "changeinfor": "Change Information", "callbackstrategy": "Callback Strategy", "pollinginterval": "Polling Interval", "externalcall": "External call", "ccompleteerror": "Cannot transfer manually", "accessorieslist": "Accessories List", "createsubtask": "Create Subtask", "copyreport": "Copy report", "recoverstep": "Recover ticket step", "redo": "<PERSON>wind before scoring", "markrepeat": "Mark repeat event", "createtask": "Create task (new subtask)", "taskstatus": "Task Status", "replycanclicktip": "Click is allowed when the reply box is not empty", "pleusernum": "Please select {target} users", "workordersuggestion": "Your suggestion for this work order...", "selecteditemsnum": "<span class='text-primary'>{target}</span> item selected", "selectrelationdialogtip": "Please confirm whether the work order you are looking for matches the current relationship type? If you need to add it, please contact the administrator!", "relationtip": "The service where the current work order is located does not have any relationship type configured, and other work orders cannot be manually associated. If you need to add it, please contact the administrator!", "transfer": "Transfer", "tasktitle": "Task title", "disassociate": "Disassociate", "unbind": "Unbind", "repeatedeventtip": "The selected tickets include tickets that have been marked as repeated events. The following tickets will be marked as the same repeated event.", "canotunbindtip": "The automatic association of the forwarding report cannot be canceled", "solutiontip": "After changing the solution, it will automatically overwrite all the content in the rich text box", "savesolution": "Save Solution", "tranknowledgetip": "Please check the content that needs to be saved, support drag and sort", "tasksaveknow": "Task saved as knowledge", "nodestatus": "Node Status", "currentnodewillflowtime": "The current node will flow at {time},", "distanceflow": "Distance Flow", "originaltask": "Original Task", "effectivenesstimesetting": "effectivenesstimesetting", "flowsetting": "Flow Setting", "scoresetting": "Score Setting", "nodesetting": "Node Setting", "linksetting": "Line Setting", "nodevalidpassed": "Node Validated Passed", "poliyuser": "Assign Processor", "erroruser": "Exception handler", "prestepassignvalid": "Assignment Processor: Mandatory to be specified by the Prestep Processor", "copyworkerpolicyvalid": "Assignment Processor: Processor of copy pre-step is required", "formworkerpolicyvalid": "Assign handler: form value is required", "assignworkerpolicyvalid": "Assign Worker: Custom Required", "assignconfigvalid": "Assign Processor: Assigner is required", "nodeerultvalid": "Node does not conform to the rule", "notselectchangelistvalid": "Creation of unassociated changelists is not allowed", "changeexistinpairsvalid": "Change creation and change processing nodes must exist in pairs", "changeonlyonerelvalid": "Change creator nodes are only allowed to be associated with one change handler node", "selectchangevalid": "The associated change creation must be selected", "selectformitemvalid": "Select at least one associated form item", "paramsMapping": "Parameter Mapping", "circulationtime": "circulation time", "circulationtimetip": "Please select the required date component in the form", "formupdateselecttimevalid": "The form component has been modified, please re-select the transfer time", "associatedsteps": "Associated steps", "transferuser": "Transfer object", "relpriority": "Relation Priority", "duration": "Duration", "circulationpath": "circulation path", "slatip": "<PERSON><PERSON><PERSON>", "priorityaging": "Priority Aging", "singleexecution": "Single execution", "periodicexecution": "periodic execution", "handoverstrategy": "handoverstrategy", "timeoutpolicy": "Timeout Policy", "limitationconditions": "Aging Conditions", "autoscoring": "Auto Scoring", "enablescoring": "Enable Scoring", "flowcannotdelete": "The process is referenced and cannot be deleted", "scoringtemplate": "Scoring Template", "disablecommettip": "Allow clicking when the reply box or attachment is not empty", "scorefavourable": "If the score is not given within the specified time period, the system will automatically praise it", "mobilereporttip": "This service only supports mobile reports", "flowname": "<PERSON><PERSON>", "relform": "Relation Form", "enableattachments": "Enable Attachments", "enabledesc": "Enable Description", "flowrult": "<PERSON><PERSON><PERSON>", "flowrulttip": "The flow is not associated with a form, and the combined conditions cannot be configured, please go to", "relchange": "Relationship Change", "changesteptodotip": "Change step set to dotip", "changestepsoptip": "When the change (sop) step is activated, set the step to do", "enablechangetodotip": "When the change processing node is activated, all change steps are set as pending", "enablechange": "When the change step is enabled", "readychange": "When the change is ready", "successjudgmenttip": "Judge by status code by default, 2xx and 3xx indicate success", "failurejudgmenttip": "Judge by status code by default, 4xx and 5xx indicate failure", "extcallnoparamtip": "Extcall no paramtip, no customization allowed", "echotemplate": "Echo Template", "callbackornot": "Whether to call back", "intervaltip": "Scheduled detection needs to configure the detection interval and status query interface address, the system will call the status query interface regularly until the correct result is obtained.", "userfilter": "Processor filter", "emptynotlimitusertip": "When not filled, do not limit the range of processors", "formscene": "Form Scene", "norelformtip": "The current process is not associated with a form, please come first", "noformscenetip": "When no form scene is selected, use the default scene", "autocirculation": "Automatic circulation", "targetparamsvalue": "Target parameter assignment", "targetparamsvaluetip": "If the selected combination tool has preset execution target, connection agreement, execution user, and batch quantity, the preset parameters cannot be modified here", "jobparamsvalue": "Jobparamsvalue", "formparamsvalue": "Form Assignment", "formparamsvaluetip": "Assign the parameters exported by the [native/setenv] tool in the current automation layout to the form for reference by downstream points", "prestepexportparam": "Upstream Export Param", "prestep": "Prestep", "assigngoals": "Assign Goals", "assignscope": "Assign scope", "formvalue": "Form Value", "nouserformitemtip": "The current association form has no optional components (user selector or drop-down box), please go to", "reviserelform": "Reviserelform", "dispatcher": "Di<PERSON>atcher", "matchall": "Match All", "matchsort": "Match Order", "preuserappoint": "Specified by preuserappoint", "copypreuser": "Copy preuser", "autoapproval": "Auto Approval", "autoapprovaltip": "Treat the processors assigned by the downstream node as an array (whether it is a single person or multiple people), when the array of downstream processors contains the actual upstream processors, the downstream will automatically approve", "actionsetting": "action setting", "getsertypeinfofail": "Failed to obtain service type information:", "savefail": "Save failed:", "empty": "empty", "programarhelp": "Grammar Help", "formaterror": "The file name '{target}' is not in the correct format", "changenodestatus": "Change node state", "changelinename": "Change Line Name", "menutype": "Menu Type", "allservicetype": "All service types", "slatags": "SLA label count", "slapolicy": "Number of SLA policies", "maxaging": "Maximum service time top3 (h)", "minaging": "Minimum service time top3 (h)", "titleexisted": "The knowledge title already exists", "rollbackto": "Fallback to", "selectservice": "Select the service to be forwarded", "changeto": "Change to", "move": "remove", "triggertime": "Triggered when {target}", "meetcondition": "Satisfy conditions", "matched": "Matched", "notmatch": "Unmatched", "templatereplace": "Template Variable Replacement", "selectchangetemplate": "Select Change Template", "variablerequire": "Variable \"{target}\" is required", "pausechange": "Pause entire change", "ignorecurrentstep": "Ignore the current step and proceed to the next step", "cancelchangestep": "Cancel Change Steps", "deadlinetime": "deadline {target}", "unassigned": "Unassigned", "selectflowpath": "Select the circulation path", "assigntosth": "Specify \"{target}\" as the handler", "viewtask": "View Work Order", "continuereport": "Continue reporting", "backtoservicelist": "Return to Service Catalog", "origintasktitle": "Original job title", "targettasktitle": "Target job title", "relateknowledge": "Related knowledge", "relknowlege": "Associated Knowledge Base", "reportinghistory": "Reporting History", "formcommonitem": "Form Common Components", "formtableitem": "Form Table Component", "automaticprocessing": "Automatic processing", "recreatenewjob": "Fallback New Job", "jobpolicy": "Job strategy", "singlecreatejob": "Single Create Job", "triggertiming": "Trigger timing", "notificationpolicysettings": "Notification Policy Settings", "templateparametermapping": "Template parameter mapping", "targetparams": "Target parameters", "selecttask": "Select Work Order", "savedraft": "Save Draft ", "signreport": "Sign a report", "signreporttip": "The first step of all approval processes is the creation and signing step, and modification is not supported. Create the handler for signing the report, which is the handler for the EOA node in the process, and set it in the process management.", "approvalpolicy": "Approval strategy", "eoadealwithusertip": "After setting the processor for the template, the processor cannot be modified during the editing process", "approvalstep": "Approval steps", "approvalprocess": "Approval process", "approvalprocesstip": "At least one node must be added outside the [Create Sign off] node", "matchmapping": "Matching mapping", "approvertip": "Fill in the approval process steps below by approver", "eoatemplate": "EOA template", "eoaautostart": "Automatically create sign offs", "eoaautostarttip": "When the handler is unique, the approval template is unique, and each approval step in the approval template has a designated approver, a signature is automatically created", "formmap": "Form mapping", "subprocess": {"waitdesc": "Synchronization: Only after all sub processes are completed can the parent process continue to flow", "formmap": {"parent": "Parent Process Form", "sub": "Subprocess form", "catalogtitle": "Service directory - {target}"}, "tab": "Subprocess"}, "manualcirculation": "Manual circulation", "eoapassedforwardedto": "When approved, the flow will be forwarded to", "eoanopassedforwardedto": "When approval fails, the flow will be forwarded to", "eoapassedforwardedtonode": "Flow node upon approval", "eoanopassedforwardedtonode": "Transfer nodes when approval fails", "jobtextparamsvaluetip": "Special usage: Use work order information such as work order number and step ID to map to job parameters. Example: Input$ {DATA.serialNumber}_ ${DATA. stepId} represents concatenating [work order number] [_] [step ID] and mapping it to job parameters.<br><br>Optional parameters:<br>&nbsp;&nbsp;&nbsp;&nbsp; Work Order Number - ${DATA. serialNumber}<br>&nbsp;&nbsp;&nbsp;&nbsp; Step ID - ${DATA. stepId}<br>&nbsp;&nbsp;&nbsp;&nbsp; Step handler - ${DATA. stepWorker}", "jobscenariovalue": "<PERSON><PERSON>", "customexpression": "Custom expression", "region": "region", "processtaskcollection": "Work Order Collection", "phase": "·Stage", "channel": "passageway", "saveandstart": "Save and start", "createcollection": "Create Collection", "taskinformation": "Work order information", "selectsteptimelinetip": "Display the timeline within the selected steps", "workcentertheadppolicy": "Control the default header order and display of this classification", "exportparameters": "[native/setenv] Export parameters, and set the scope of the environment variable to all nodes", "eoaviewstep": "View steps during approval", "complatenode": "Transfer nodes", "totaljobnumtarget": "A total of {target} assignments", "runningcounttarget": "{target} in progress", "completedcounttarget": "{target} completed", "failedcounttarget": "{target} failed attempts", "changecreatewithouthandler": "Please create a node associated with the change processing node for the change", "viewauth": "View Authorization", "viewauthtooltip": "The current logged in person must meet both the authorization scope of the current directory and all parent and child directories in order to view work orders created through services in the current directory.", "reportauthtooltip": "The current logged in person must meet the following requirements in order to create a work order through the services in the current directory: within the authorization scope of the current directory, as well as all parent and child directories and services.", "formtag": "Form label", "formtagtip": "When the label is empty: all form components in the main scene are form components; When the label is not empty: The form component is configured for the selected label.", "stepfilter": "Process steps", "stepdesc": "Activated steps", "savetransfer": "Save and transfer", "savetransfertip": "Save the current form and forward it", "thirdpartyapi": "Third party callback interface", "reactivatestep": "Reactivation steps", "seeprocessconfig": "View the current work order process configuration", "editprocessconfig": "Edit the current work order process configuration", "stepishandledby": "Step Handling Human", "sercalendar": "Service Calendar"}, "autoexec": {"addrootdirectory": "Add root directory", "editdirectory": "Edit Directory", "deletedirectory": "Delete Directory", "deleteservice": "Delete Service", "editservice": "Edit Service", "addservice": "Add service", "searchservice": "Search Service", "citecatalognodelete": "Citecatalognodelete cannot be deleted", "citeservicenodelete": "Cited services cannot be deleted", "servicedescription": "Service Description", "batchsetting": "Batch setting", "batchquantity": "Batch Quantity", "executetarget": "Execution Target", "executeaccount": "execute account", "jobparam": "Job Parameters", "combinationtool": "Combination Tool", "citeform": "Citation Form", "roundcountdescrition": "Divide the execution target into N batches according to the quantity, and execute them successively", "setbantchnumbernoupdate": "The combination tool has set the batch number, which cannot be modified", "immediateexecution": "Run", "jobname": "Jobname", "otherparam": "Other parameters", "customtoollibrary": "Custom Toollibrary", "toollibrary": "Tool Library", "hasauthinfo": "Users with permission can add custom tools or tool library tools under the category to combined tools", "hasauthtip": "Users with permission can audit versions under category in composite tool", "auditauthorization": "Audit Authorization", "libraryusageinstructions": "Tool library usage instructions", "alltemplates": "All templates", "directorytool": "Tool Directory", "resourcelock": "Resource Lock", "controlpanel": "<PERSON><PERSON><PERSON>", "exportjob": "Export Job", "copyjob": "Copy <PERSON>", "validatejobtip": "Confirm to validate the current job? After the job is validated, no further operations can be performed.", "loghaswarninfo": "There is warning information in the log", "phasehastips": "There is an execution node with state {target} in the phase", "environmentvariable": "Environment Variable", "paramsdetail": "Parameter Details", "paramsname": "parameter name", "paramsvalue": "parameter value", "skipallsuccessignorenode": "Skip all successful, ignored nodes", "rerunallnode": "Rerun all nodes", "operationlevel": "Operation Level", "uploadTooltip": "The preset parameter set information cannot be imported. If the preset parameter set is introduced in the import tool, please add it after importing", "timingjob": "Schedule job", "timingscheduling": "Tim<PERSON>", "timesetting": "Time Setting", "globalparameter": "Global parameter", "displayname": "Displayname", "valuetype": "value type", "associatedobject": "AssociatedObject", "presetparameterset": "Preset parameter set", "toolparameter": "Tool parameter", "associatedtool": "Associated Tool", "owningsystem": "Owning system", "screeningmode": "screening mode", "inputtext": "Input text", "inputtextdesc": "According to the specified format, manually enter single or multiple node information", "filterdesc": "Create a filter to select the specified range of nodes as the execution target", "nodedesc": "Customize query conditions, filter and check nodes as execution targets", "citejobparamdesc": "Cite job parameters as execution targets", "citeupstreamparamdesc": "Cite upstream node output parameters as execution targets", "upstreamparameter": "Upstream parameter", "assetstatus": "Asset Status", "subordinatedepartment": "subordinate department", "maintenanceperiod": "Maintenance Period", "executionrecord": "Execution Record", "chooseonecombinetooldesc": "Choose at least one combined tool", "configdesc": "Configuration is a piece of javascript code that needs to define an object containing properties such as methods, for example:", "templatedesc": "The template needs to conform to the Vue template syntax specification, and must specify a root tag, usually a div, which supports all iView components and also supports the following internal components:", "testdatajson": "The test data needs to conform to the json format", "parentnodeid": "parent nodeid", "associateacustomtool": "Associate Custom Tool", "disassociatecandeletecatalog": "The current directory or its subdirectories have been associated with a custom tool, and this directory can only be deleted after disassociate", "bysubcatalogalldeletetips": "When deleting the current catalog, all its subdirectories will be deleted at the same time", "ipformattip": "The host target input format is IP, the service target input format is IP:PORT, the database target format is IP:PORT/SID, one target per line", "viewalltarget": "View more {target} targets", "choosetagasnodetip": "Choose the tag filter node as the execution target", "jobparamnosettingtip": "The [node information] parameter is not set in the job parameters", "targetip": "target name, ip", "inspectstatus": "Inspection status", "batchexportdata": "Batch import data", "batchedittip1": "Each line represents an option, and the value of each line cannot be repeated;", "batchedittip2": "The value and the display text are separated by \",\", you can only have the value, and the display text will be automatically supplemented;", "batchedittip3": "The length of the array cannot be greater than 500", "verifydifferentvalues": "The values corresponding to each row cannot be the same", "datasourcesetting": "Datasourcesetting", "selectionrange": "Selection Range", "filterchoosenodedesc": "Customize query conditions, filter and select nodes", "setfilterdesc": "Set filter to narrow the range of selectable nodes", "selectnode": "Select node", "static": "static", "filetype": "FileType", "tool": "Tool", "freeparameter": "Free parameter", "failurestopinfo": "Failure stop: After any target fails to execute the current tool, the target stops executing subsequent tools, and other targets stop executing the next stage after executing all tools in the current stage normally", "failurecontinueinfo": "Failure to continue: when any target fails to execute the current tool, the target skips the current tool and continues to execute subsequent tools and stages, and other targets are not affected", "satisfiedexecution": "Satisfied execution", "otherwiseexecute": "Otherwise execute", "tooldeleted": "The current tool has been deleted", "tooldescription": "Tool Description", "parameterenglishname": "Parameter English name", "parameterchinesename": "Parameter Chinese name", "controltype": "Control Type", "editable": "Editable", "toolnamedescription": "Tool name, description", "customtool": "Custom Tool", "timingplan": "Schedule", "planstarttime": "Plan start time", "planendtime": "plan end time", "executetargettips": "Only support selecting assets containing the test tag as execution targets", "selectcombinationtool": "Select Combination Tool", "job": "job", "jobwarninginfo": "There is warning information in the job", "jobignoreinfo": "There is an execution node whose status is ignored in the job", "triggertype": "trigger type", "toolclassification": "Tool Classification", "executionsituation": "Status", "jobsource": "Job Source", "operator": "Operator", "executiontime": "Execution time", "taskexpirationtip": "In order to prevent the task from expiring immediately after submission, it is only allowed to select a time after 5 minutes.", "takeoverjob": "Takeover job", "factoryclassnodelete": "The current classification is the factory default classification and cannot be deleted", "nosavetip": "You have unsaved changes", "scriptvalidsuccess": "<PERSON><PERSON><PERSON> validation succeeded", "deletelastversiontip": "There is only one version of the current custom tool, deleting this version will cause the current custom tool to be deleted.", "draftname": "Draft Name", "createversion": "Create Version", "islibraryfile": "Is it a library file", "risklevel": "Risk Level", "jobdetailspecialinfo": "Used to display personalized data on the job details page", "pipeline": "pipeline", "submitnewversionwait": "Submitted a new version, waiting", "deletedversion": "Deleted version", "noexecuteauthrelateadmin": "No execution permission, please contact the administrator!", "activecombinetooltip": "The current combined tool is disabled, please activate the combined tool before executing", "stagegroup": "Stage Group", "presetparameter": "Preset parameter", "maintainer": "Maintainer", "setinputcondition": "Set: Please enter a condition", "phasedeletetip": "It has been deleted, please clear the tool in the combined tool and save", "phasesetpresetparamtip": "Settings: Please select a preset parameter set", "phasesetfreeparamtip": "Setting: Incomplete free parameter data", "phasesetinputparamtip": "Settings: input parameter data is incomplete", "scriptparser": "<PERSON><PERSON><PERSON>", "scriptcontent": "Script Content", "rejectreason": "Rejected approval, reason:", "selectcomparativeversion": "Please select the version to be compared in the table below", "toolreadyaudit": "Tool pending audit", "backtolibrary": "Backtolibrary", "relatecombinationtool": "Relate Combination Tool", "dependenttool": "Dependent Tool", "inputparamerror": "The input parameter data is not filled correctly", "outputparamerror": "The output parameter data is not filled correctly", "selectparser": "Parser: Please select", "inputscriptcontent": "Script content: please enter content", "inputfreeparam": "Free parameter: please enter the content", "scriptcontenterror": "Script content: does not comply with the rules", "closefreeparamtip": "When closing free parameters, the set free parameters will be deleted, continue to close?", "ignorephaseconfirm": "Confirm to ignore the current phase?", "jobruntip": "Ignored nodes will be skipped when the job is rerun or continued", "paramdownload": "Parameter download", "standardoutput": "Standard output", "runscript": "Run script", "executionresult": "Execution result", "runrecord": "Run record", "resetnodeconfirm": "Resetting the node will change the node status to running, whether to reset the node", "resetnodescene": "Resetting nodes is suitable for the following two scenarios:", "waitrunnodetip": "The node needs to be re-run. After resetting to the waiting to run state, the node can be re-run.", "malfunctionrerunjob": "The platform is faulty, causing all nodes to stop running. You can reset all nodes and rerun the current job.", "ignoreselectednode": "Whether to ignore the selected node", "deletnodetip": "The selected node includes waiting, running, and successful nodes, and the ignore operation cannot be performed. Whether to remove waiting, running, and successful nodes, and ignore other nodes", "rerunselectednodeconfirm": "Whether to rerun the selected node", "noenvproducts": "No environmental products", "selectednodeinclude": "Selected node includes", "ignoreselectednodeconfirm": "Confirm to ignore the selected node?", "deletenodecontinue": "No<PERSON>, do you want to delete these nodes and continue?", "ignorecount": "<b>{target}</b> ignore", "pendingcount": "<b>{target}</b> pending", "successcount": "<b>{target}</b> succeeded", "runningcount": "<b>{target}</b> running", "noignorenode": "The currently selected node has no ignorable nodes, please reselect", "nodedetail": "Node Details", "reexecutecurrenphase": "Skip all successful, ignored nodes and re-execute the current phase?", "executephase": "Execution Phase", "addtotarget": "Add to target", "notaddtotarget": "No target added", "importsuccesstarget": "Successfully imported {target} targets;", "formaterror": "The format is incorrect;", "targetrepetition": "Target repetition;", "issaveexecutetarget": "Whether to save the execution target", "notmatchexecutetarget": "Under the current filter, no matching execution target was found", "addaccount": "Add account", "thefollowingobjectives": "Thefollowingobjectives:", "addbindaccount": "Add Bind Account", "notfoundassetstip": "The corresponding asset was not found in the asset list, and the above target will be skipped during execution", "ignoretargettoassetpage": "The above target will be skipped during execution, and you can go to the asset list page", "nousertoaccountpagesetting": "The account for Connection Agreement: {protocol}, executing user: {executeuser} was not found. You can go to the account management page", "noexecuteusertoassetmanage": "The account for Connection Agreement: {protocol} was not found. Execution may cause an exception. The account needs to be bound.You can confirm tagent Manager page that the tagent is connected", "tagentnoexecuteusertoassetmanage": "The account for Connection Agreement: {protocol}, Execution User: {executeUser} was not found. Execution may cause an exception. The account needs to be bound.You can go to the asset list page", "publishcombinetool": "Publish Combine Tool", "executecount": "Execution count", "starttoexecute": "Executed after startup:", "lastactivetime": "Last active time", "lastcompletetime": "last completed time", "pleaseinputversionname": "Please enter a version name", "addjob": "Add job", "batchcountdisabledesc": "Divide the execution target into N batches by quantity and execute them sequentially.", "choosethetime": "Choose the time", "nowriteusertooltip": "If you do not fill in the user here, the current stage will inherit the combination tool execution user by default. If you fill in the user here, the current stage will use the execution user filled in here; when executing, the execution user cannot be modified", "batchcountprioritydesc": "Divide the execution target into N batches according to the quantity, and execute them successively. The priority of the number of batches set in the stage is higher than the number of batches set in the combination tool or job", "executeTooltip": "If you do not fill in the execution target here, the current stage will inherit the execution target of the combination tool by default. If you fill in the execution target here, the current stage will use the execution target filled in here; during execution, the execution target of the stage cannot be modified", "valueshowtext": "value, display text", "ifblockconditiongramdescription": "IF-Block conditional grammar description:", "comparativesupport": "Comparative support:", "filedetection": "File detection:", "filedelink": "(corresponding to file, directory, existence, soft link judgment)", "logicaloperation": "Logical operation:", "availableenvironmentvariables": "Available environment variables:", "applyname": "Application Name", "modulename": "modulename", "envname": "Environment Name", "instanceip": "Instance IP", "instanceport": "Instance Port", "instanceprotocolport": "Instance Protocol Port", "versioncatalog": "version catalog", "forexample": "Example:", "setfilterexecutelimitdesc": "After setting the filter, the execution target can only be selected within the scope of the filter", "jobparamnotconfignodeinfoparamdesc": "The parameters of the [node information] type are not set in the job parameters", "allphasesetconfigtargetaddjobdesc": "If targets have been configured for all phases, users do not need to specify targets when creating jobs", "settodefaultscenario": "Set as default scenario", "this scenario has configured": "This scenario has configured", "pleaseselectatleastonephase": "Please select at least one phase", "exportnode": "Export Node", "enterthenodenameoripaddress": "Please enter the node name or ip", "skipallsuccessoperationcontinue": "Skip all successful operations and continue with other operations?", "actuatorinformation": "Actuator Information", "sqlmanifest": "SQL manifest", "savejob": "Save job", "isdataproperties": "Is the properties in the data", "pleaseselectcategory": "Please select a category", "pleaseselectdirectoryname": "Please select a directory name", "pleaseselectuploadfile": "Script content: Please upload the attachment", "supportonlytarfile": "Only support tar files", "scriptcontenttips": "Help:\nTool execution uses standard command line parameters to pass parameters. \nInput parameters:\n1) Parameters with parameter names:\noption name is the parameter name, option value is the parameter value. \n2) Parameters without parameter names: \nCorresponds to the free parameters of the tool, and obtains the parameters through the parameter serial number. \nHow different programming languages ​​handle command line parameters, please check the relevant demos for details. \nOutput parameters:\nThe parameters are output by writing the output parameters to the json file ouput.json in the current job working directory. \nThe platform provides output packages in different languages. For details, please view the relevant demos, for example:\nPython:\nfromlibimportAutoExecUtils\nout={}\nout['outtext']='thisisthetextoutvalue'\nAutoExecUtils.saveOutput(out)\nPerl :\nuseAutoExecUtils;\nmy$out={};\n$out->{outtext}='thisisthetextoutvalue';\nAutoExecUtils::saveOutput($out);\nBash:\nouttext='thisisthetextoutvalue'\nif[!- z'$OUTPUT_PATH'];then\ncat<<EOF>'$OUTPUT_PATH'\n{\n'outtext':'$outtext'\n}\nEOF\nfi\nExit code identification:\nAs long as ExitCode is not equal to 0 represents an abnormal exit. \nNote:\nIf you need to initiate a child process, please be sure to close the standard output and error output after execution, otherwise the parent process may not exit normally.", "speedlevel": "Speed Level", "workthread": "Worker thread", "loadedjob": "Loaded job", "jobmodule": "Operation module", "jobhandler": "Job Processor", "servicehasfailed": "Service has expired", "currenttoolisalreadydependentonanothertool": "The current tool is already dependent on another tool,", "deletefailedcombinatetoolpresetcitetips": "Deletion failed. Please check if it has been referenced by the combination tool or preset parameter set", "usedtooldesc": "The current type has been referenced by a tool and cannot be deleted", "targetjoberror": "{target} Failed to create job", "toolparamstooltip": "Tool parameters are a set of parameters for associated tools. Parameters from custom tool libraries can be added, deleted, and modified, while parameters from tool libraries cannot be added, deleted, or modified.", "combophasexpired": "The combination tool has expired", "pleasereedit": "Please re edit and submit for review", "abortjob": "Terminate assignment", "runnergroupdeprecatedtips": "The parameters of the task executor group have expired. Please click confirm again and save the process", "loopitem": "Loop item", "loopoperation": "Loop execution", "loophelp": "Execute in a loop according to the loop item, draw branches to the environment variable ${LOOP_ITEM}", "loopitemname": "Loop item name", "loopitemhelp": "Set environment variable aliases for loop items and use them within the loop body", "setinputloopitemvar": "Please enter the loop variable", "setinputloopitems": "Please enter the loop item", "setinputloopoperations": "Settings: Please enter at least one tool", "runnergrouptagprocesstips": "If form assignment is required, please set the executor group to the \"Job Parameters\" type in the combination tool", "runnergroupprocesstips": "If form assignment is required, please set the \"Executor Group\" to the \"Job Parameter\" type in the combination tool", "resetrunnerphase": "Reset phase or not", "runnerGroupTooltip": "If the actuator group is not filled in here, the current stage will inherit the combination tool actuator group by default. If the actuator group is filled in here, the current stage will use the one filled in here; During execution, the executor group of the stage cannot be modified", "isfirenext": "Do you want to activate the next group", "inputnodelimit": "The quantity cannot exceed 1000", "parall": "Concurrent quantity", "paralldesc": "Execute the execution targets in order of concurrency.", "jobrecord": "Automated job recording", "jobcount": "Number of automated tasks"}, "framework": {"roleauth": "Authorize immediately after creating the role", "rolename": "Role Name", "roledesc": "Role Description", "rolecount": "Number of roles", "usercount": "Number of users", "teamcount": "Number of teams", "teamname": "Team name", "authname": "Authority Name", "belongmodule": "belonging module", "authdesc": "Authority Description", "calendar": "Scheduling", "inheritselected": "Inherit Selected", "selectsubnode": "Select subnode", "printsnapshot": "Print Snapshot", "runtimesort": "Descending by execution time", "timecostsort": "Descending by time spent", "monitorlist": "Monitoring statement list", "datapool": "Database Connection Pool", "totalconnections": "Total connection", "activeconnections": "Active connections", "awaitingconnections": "Waiting for connection", "freeconnections": "idle connection ", "sqlsstatement": "SQL statement", "settingsql": "Set SQL", "addmonitor": "Increase monitoring", "delmonitor": "Delete Monitoring", "corelibrary": "Core Library", "subsidiarylibrary": "Subsidiary library", "datafootprint": "Data footprint", "indexfootprint": "Index footprint", "datafreespace": "Data free space", "targetdayago": "{target} days ago", "selectintgn": "Edit Selection Integration", "upconfigfile": "Please upload the configuration file", "matrixuniquekey": "Unique identification of matrix", "integrationsetting": "Integration Settings", "cidata": "Model Data Source", "matrixprimarykey": "Matrix primary key", "emailtest": "Mail Test", "smtphost": "Smtp host", "smptport": "Smtp port", "head": "Request Header", "inputtrans": "input translation", "outputtrans": "Output conversion", "dataspecification": "Data specification", "reqsetting": "Request Settings", "requestFrom": "Request Source", "syncreport": "Synchronize records", "syncdata": "Synchronize data", "cronexpression": "Scheduled execution", "xmlconfig": "Configuration Content", "syncmode": "Synchronous mode", "appendmode": "append mode ", "replacemode": "Replacement Mode", "syncnow": "Sync Now", "isexpire": "Expired or not", "expired": "Expired ", "notexpired": "Not expired", "inputtype": "Input Control", "iscondition": "As a condition", "copyversion": "Copy Active Version", "subscibe": "subscription management ", "topic": "Theme management", "subscribe": "subscribe", "isdurable": "Subscription Method", "dursubs": "durable subscription ", "tempsubs": "Temporary subscription", "batchupgrade": "Bulk upgrade", "batchreboot": "Batch restart", "batchresetcred": "Batch Update Password", "installpackage": "Installation package management", "tagentinstall": "Tagent Installation", "connected": "Connected", "notconnected": "Not connected", "reqparamstip": "Identification is a required parameter", "logdownload": "Log Download", "upgradeversion": "Upgrade version", "pkgversion": "Version number", "ostype": "OS Type", "runnergroup": "Proxy Group", "tagentsearchpla": "Include IP/Name/OS Version", "includeip": "Include IP", "osversion": "OS/OS version", "runnerlist": "Runner List", "ipnetwork": "Network segment IP/subnet mask", "runnercount": "Number of Runners", "upgrecord": "Upgrade Record", "upgagentcount": "Number of upgrade agents", "upgtime": "Upgrade time", "sourceversion": "Original version", "targetversion": "Target Version", "upgdetails": "Upgrade Details", "nettyport": "NETTY port", "proxygroup": "Proxy Group Name", "mask": "Mask", "tagentlist": "Agent list", "selectpackage": "Select an existing installation package", "autoinstall": "Automatic installation", "manualinstall": "Manual installation", "manualuninstall": "Manually Unloaded ", "ipportselect": "Select according to IP: PORT", "networkselect": "Select based on network segment", "tagentnotfind": "The heartbeat of the agent was not found", "runnertnotfind": "Runner not found", "viewdownload": "To view the complete results, please download", "invertedorder": "Reverse order", "positiveorder": "positive sequence", "apihelp": "Interface Help", "selectyear": "Established year", "calendarinfor": "Shift scheduling information", "workinghours": "Working hours", "i": "I", "d": "D", "c": "C", "q": "Q", "o": "O", "activedversion": "Activate version", "rowreaction": "Row linkage", "formwidth": "Form width", "saveothernewversion": "Save as New Version", "customitem": "Custom Components", "layoutwidget": "Layout Widget", "automationwidget": "Automation Widget", "cmdbwidget": "Configuration Widget", "actuator": "Actuator management", "osbit": "CPU architecture", "mem": "Memory", "theme": {"themecolor": "Theme color", "headcolor": "Head color", "tablecolor": "Table Color", "formcolor": "Form Color", "menucolor": "Left Menu Color", "otherscolor": "Other colors", "dashboardcolor": "Dashboard color", "logoimg": "LOGO image", "themehovercolor": "Theme color mouse through color", "themeactivecolor": "Theme color activation color", "headbg": "Head background", "headbghover": "Head background mouse passing color value", "tablestripecolor": "Table Zebra Color", "tablehovercoler": "Table Move Up Color", "tableseleccolor": "Table selection color and main color weakening", "thcolor": "Meter head color", "checkboxcolor": "Multiple check boxes select background color", "selectcolor": "Dropdown selection and mouse over color values", "swithbg": "Switch background color", "formdisabledcolor": "Multiple selection and single selection (disabled, selected state)", "menuselectcolor": "Left menu selection color", "menuhovercolor": "Left menu mouse over color", "graycolor": "Auxiliary color", "warnbg": "Warning background color", "chartcolor": "Chart Color", "valuebg": "Value Graph Background", "valuetextcolor": "Value Graph Text", "logosetting": "LOGO image configuration", "customlogo": "Custom logo"}, "triggeraction": "Only for those with actions", "nottriggeraction": "Only looking at inactivity", "notifyhandler": "Notification method", "tovouser": "Recipient", "nextfiretime": "Next execution time", "reqcondition": "Please fill in the conditions completely", "sysparamsnotedit": "System parameters do not allow operation", "freemarkerhelp": "Freemarker syntax help", "apiaccesstime": "Access record retention period", "apitest": "Interface testing", "formrequesttype": "forms mode ", "jsonrequesttype": "JSON mode", "timeout": "prescription of claim", "qps": "Access frequency", "apirecord": "Interface call record", "calltime": "Call time", "direction": "Arrangement direction", "openmode": "Open With", "jumpaddress": "Jump Address", "hrefblank": "New Window", "hrefself": "current window", "later": "greater than", "earlier": "less than", "laterandequal": "Greater than or equal to", "earlierandequal": "Less than or equal to", "equal": "Fill in the current day", "lessthan": "Fill in before the current day", "greaterthan": "After filling in the day", "othersdate": "Specify date", "selectlevel": "Dropdown progression", "grade": "level", "leveloptions": "Level Options", "selectmode": "Selection method", "matrixattr": "Matrix Properties", "pcshowtd": "PC display column", "mbshowtd": "Mobile Display Column", "searchcondition": "search criteria", "extraattr": "Extended Properties", "dialogmode": "Popup selection", "normalmode": "Direct selection", "tablematrix": "Table Matrix", "showtextfields": "Show Text Fields", "thsetting": "Header settings", "defalinenum": "Default number of rows", "compval": "Component value", "insetdown": "Insert One Line Down", "insetup": "Insert a line up", "insetleft": "Insert a column to the left", "insetright": "Insert a column to the right", "delrow": "Delete current row", "delcol": "Delete current column", "layoutmethod": "Layout method", "fixedratio": "Fixed ratio", "fixedpixels": "Fixed pixels", "canvaswidth": "<PERSON> Width", "cusscenename": "Custom Scene Name", "impactrow": "Impact Row", "hiderow": "Hide Rows", "displayrow": "Show Rows", "reactionsetting": "Linkage settings", "targetline": "Line {target}", "inputdata": "input data", "formcomplistlable": "Component list (click to copy component uuid)", "outputdata": "output data", "simulatedsubmission": "Simulated submission", "assignment": "assignment", "inheritscenesetting": "Inherit default scene configuration", "hideclearval": "Clear values when hidden", "ishasvalue": "Is there a return value", "compconfigtemp": "Component Configuration Template", "comptemp": "Component template", "userselect": "User Selection", "treeselect": "Tree Selection", "tableselect": "Table Selection", "tableinput": "Table Input", "formdivider": "Division line", "cubeselect": "Matrix selection", "formcollapse": "Folding panel", "formckeditor": "Rich Text Box", "formcascader": "Cascading dropdown box", "changepriority": "Modify Priority", "formcientitymodify": "Configuration item modification", "formcientityselector": "Configuration item selection", "filestandard": "Please refer to the attachment specifications", "cusvalid": "Custom verification", "timeneed": "Time required", "dragformcomp": "Please drag in the form component", "datenotworktime": "The date is not within the working time range", "dateneed": "Date required", "currdate": "Current filling date", "valfieldmapping": "Value Field Mapping", "showtextfieldmapping": "Show Text Field Mapping", "groupname": "User Group Name", "selectedchild": "Selected child nodes", "unselectedchild": "No sub nodes selected", "selectedtarget": "Select Target", "notcancelauth": "Inherit role permissions and do not allow revocation of authorization", "filterbypermission": "Filter by permission", "filterbyrole": "Filter by Role", "importfromuserlist": "Import from user list", "importfromusergroup": "Import from user group", "position": "post", "parentpath": "Superior Path", "groupleader": "Group leader", "subgroup": "Subgroup", "roleid": "Role ID", "menberlist": "Member List", "grouplist": "Group List", "batchauth": "Volume Authorization", "returntogrouplist": "Return to group list", "continuecreate": "Continue creating", "notips": "Don't prompt again", "saveusergroupchange": "Do you want to save changes to the current user group?", "viewuser": "View Users", "saveuserchange": "Do you want to save the changes to the current user?", "backtouserlist": "Return to user list", "editinsetting": "Can be modified in personal settings", "userlevel": "User level", "setpwd": "Set password", "confirmpwd": "Confirm Password", "pleaseconfirmpwd": "Please confirm the password", "inputphonenumber": "Please enter the phone number correctly", "searchcontent": "Search Content", "grouprole": "Group Role", "userlist": "User List", "pwdnotsame": "Inconsistent input before and after two passwords", "userpermission": "User permissions", "changeavatar": "change", "previewavatar": "Avatar preview", "uploadagain": "Re upload", "missionauth": "Task Authorization", "authservise": "Authorization Services", "startlargethanend": "The start time cannot be greater than the end time", "authuserisrepeat": "Authorized users cannot be duplicate", "authserviceisrepeat": "Authorization services cannot be duplicated", "recreate": "Regenerate", "modifypwd": "Change Password", "custom": "individualization", "popupalert": "Pop up reminder", "defaultitem": "Default Options", "token": "token", "currentpwd": "Current password", "newpwd": "New password", "pleaseconfirmnewpwd": "Please confirm the new password", "widgettype": "Component Type", "prioritystatistics": "Priority statistics", "valuechart": "Numerical graph", "itsmworkorder": "ITSM work order", "scope": "Applicable scope", "optionalrange": "Optional range", "dataasyncconfirm": "Are you sure to perform data synchronization?", "configexample": "Configuration Example", "parsing": "Grammar analysis", "saveexpire": "Retention period of synchronized records", "userselector": "User selection box", "enumselector": "Enumeration selection box", "flowlist": "Process Reference List", "deletealert": "Delete prompt", "deleteversionconfirm": "Are you sure you want to delete the<b>{target} version</b>form", "deletecurrentversionconfirm": "Are you sure you want to delete the form from the current version", "extendwidget": "Extension components", "widgetinfouncomplete": "Incomplete component information filling", "linkagewidgetuncomplete": "Incomplete filling of component linkage in form settings", "getformdatafail": "Failed to obtain form data", "errorinfo": "error message", "multi": "Multiple", "rebuildconfirm": "Reconstruction confirmation", "rebuilddesc": "Are you sure to rebuild the search index for the current type?", "incrementalrebuild": "Incremental reconstruction", "allrebuild": "renew all", "accessaudit": "Access Audit", "requestadress": "Request Address", "paramtype": "Parameter Type", "requestparam": "Request parameters", "sendingparam": "push parameter", "transferresult": "Conversion results", "requestfail": "Sending test request failed", "paramformaterror": "The parameter format does not conform to the JSON format", "noparam": "No request parameters", "grammarhint": "Grammar prompts", "connecttimeout": "connection timed out", "readtimeout": "Read timeout", "paramcheckdesc": "The parameter description is used to provide external call assistance. If the system needs to verify parameters based on the parameter description, please activate the following options.", "checkenable": "Activate verification", "aftercheckdesc": "After activating verification, the parameters in the non parameter description list will be discarded.", "transfertest": "Conversion testing", "output": "output", "authorizedto": "Authorize to", "issuedate": "Date of issuance", "expiredate": "Expiration date", "serveceenddate": "Service Termination Date", "usedmatrixdesc": "The current matrix has been referenced by a form, and the model data source cannot be modified", "acmenulist": "Menu List", "firstlevelmenu": "First level menu", "parentmenu": "Parent menu", "isenable": "Enable or not", "defaultopen": "Default Open", "newtab": "New Tab", "selecticon": "Select icon", "iconselect": "Icon selection", "childmenu": "Submenu", "formuncomplete": "Complete the form!", "noticeeffitivetime": "Effective date of announcement", "nostarttimedesc": "If no start time is specified, the announcement will be issued immediately; If no end time is specified, the announcement needs to be manually stopped.", "nopopupdesc": "When reissuing, users who have viewed the previous announcement will no longer receive pop-up reminders.", "associationgroup": "Association Group", "deleteconfirm": "delete", "externalauthen": "External authentication", "authenprotocol": "Authentication Protocol", "nameandip": "Name, IP", "unzipto": "Decompress to", "or": "or", "and": "And", "saverolechange": "Do you want to save changes to the current role?", "backtorolelist": "Return to Role List", "defaultpolicy": "Default Policy", "defultpolicysetting": "Default Policy Settings", "syncsceneattr": "Synchronize default scene attributes", "matrix": {"isoverride": "The same Matrix of ones already exists in the current environment. Do you want to overwrite it?"}, "corpid": "Enterprise ID", "corpsecret": "Applied credential key", "agentid": "Enterprise Application ID", "smptsslenable": "Using SSL", "shutdown": "Shutdown", "host": "server address", "heartbeattime": "Last heartbeat time", "heartbeatrate": "heart rate ", "heartbeatthreshold": "Heartbeat threshold", "versionlog": "Version Log", "exporttable": "Export Table", "importtable": "Import Table", "excelinputtemplate": "Table Input Template", "formsubassembly": "List subcomponents", "globalreadonly": "Global read-only", "globalreadonlytip": "The global read-only priority is higher than the internal read and write settings of the component. After turning on the global read-only switch, all components in the scene are in a read-only state", "iscannotdeletenode": "There are child nodes that cannot be deleted", "requiredselectedtips": "If the current component is set as mandatory and only one option remains after filtering, the system will automatically select that option", "formtableinputercomponent": "Table Input Component", "tablecomponent": "Table component", "internalmenu": "Internal menu", "custommenu": "Custom menu", "internalmenuoperationtips": "The internal menu can only be viewed with permission, and cannot add an editing menu", "hidetab": "<PERSON><PERSON>", "commercialauth": "Exclusive permissions for commercial modules", "regiondispatcherapp": "Business system", "regiondispatcherapptooltip": "Please select the business system form", "regiondispatchermatchtooltips": "Dispatcher Property Mapping Matrix Properties", "compkeyname": "Component English Name", "writetime": "Fill in the time", "timevalue": "Time value:", "timevaluedesc": "0: Represents the value of \"Custom | Fill in Time | Date\".", "positiveintegerdesc": "Positive integer: Indicates that the value of \"Custom | Fill in Time | Date\" increases by the corresponding time (or by the selected time unit).", "neglectintegerdesc": "Negative integer: Indicates that the value of \"Custom | Fill in Time | Date\" is reduced by the corresponding time (or by the selected time unit).", "writetimedesc": "Fill in time: indicates the time when the current component validation was triggered.", "fieldsconfiginfo": "According to the field mapping configuration, obtain the relevant information of the work order reporter", "jsscriptconversion": "JS Script Conversion", "strjoin": "Field concatenation", "expressionplaceholder": "Please fill in the ES5 script and return the value of the expression as' value '. Example:", "matrixsaveconfirm": "The matrix has been referenced, are you sure to make the modifications?", "linkageassignment": "Linkage assignment", "conditionassignment": "Conditional assignment", "hideattrassignment": "Hidden attribute assignment", "allowadd": "Can be added", "allowdelete": "Can be deleted", "mqtype": "Message queue type", "notavailable": "Not available", "mqhandler": "Message queue component", "showimportexporttemplatetableoptions": "Display export/import (template, table) options", "componentnoexist": "component does not exist", "custommaxtrixselectaddbtndesc": "When selecting a custom matrix type and adding data, the dropdown menu will display a \"+\" button.", "showcomponentnamesintab": "Display the component names dragged into the Tab tab", "hideheaderwhendataempty": "Hide header when data is empty", "formsubassemblytabname": "Tab Name", "formsubassemblytabnametip": "The values and hidden attributes of text boxes and drop-down boxes can be used as tab names", "tagentupgrade": "Upgrade Package Management", "tagentupgradepkg": "Upgrade Package", "selecttagentupgradepkg": "Select an existing upgrade package", "tagentuploadupgradepkg": "Upload upgrade package", "versionremarks": "Version Remarks", "heartbeaturl": "Backend server address", "runnergrouptips": "The request for login authentication needs to carry a header as a rule expression (note that the header parameter in the expression must be all lowercase and the prefix must be {prefix}). If the value of the expression after execution is true, the executor group will take effect, and false and syntax exceptions will not take effect. For example:", "batchdeletetagenttooltip": "Tagent in connected state cannot be deleted"}, "knowledge": {"document": "Document", "knowledge": "knowledge", "intellectualcircle": "Group", "approver": "Approver", "member": "Member count", "documentcount": "Document count", "knowtype": "Knowledge Type", "knowtypename": "Knowledge Type Name", "approve": "Approval", "rejected": "Application rejected, reason", "savedraft": "Save Draft", "primaryclassification": "Primary Classification", "samedirectory": "Same directory", "navigationdirectory": "Navigation Directory", "notresult": "No result", "searchresult": "A total of {target} results were found for you", "outtopage": "Link to external page", "searchdockeyword": "Search Documentation Keywords", "selectinlink": "Select internal link", "jumptolink": "Jump to external link: {target}", "viewdoc": "View Documentation", "backknowlist": "Back to knowledge list", "confirmreleasenewvers": "Confirm Release Newvers", "docamount": "Amount of Documents", "tooltip": {"approver": "Approvers have permission to view, edit, approve and delete all knowledge documents in the knowledge circle", "member": "Knowledge circle members have permission to view and edit all knowledge documents in this circle", "documentcount": "This statistic includes the number of approved and unapproved knowledge", "sumbit": "The document is pending review, you can view the review progress in the document's activity", "template": "Note: After the template is selected, the new template will overwrite all the contents of the current document", "replace": "The current keyword contains links, replacement is not allowed", "replace1": "The text at {n} has been replaced, keywords containing links are not allowed to be replaced, and have been automatically skipped", "replace2": "The text at {n} has been replaced", "search": "Turn off search and replace", "editdocumentapprove": "Modified the shared document [{target}], pending"}, "imageloadding": "Loading pictures", "imageloadfail": "Image loading failed", "editor": "editor ", "leftalign": "be at the left side", "rightalign": "be at the right", "imagedesc": "Picture description", "toinsidelink": "Jump to internal link:", "sharefile": "Shared Documents", "waitto": "wait to", "forward": "forward", "norelateknowledge": "No relevant knowledge", "strikethrough": "strikethrough ", "uploadimages": "Upload images", "enterlinkdescription": "Enter link description"}, "report": {"report": "Report", "reporttemplate": "Report Template", "reportname": "Report Name", "reportdetail": "Report Details", "sendplan": "Scheduled delivery", "editreport": "Edit report", "addreport": "Add Report", "previewreport": "Report Preview", "screen": "big screen", "screenmanage": "Large screen management", "valuefield": "Value Field", "textfield": "Text Field", "displayfield": "Displayfield", "contentconfigexample": "Content Configuration Example", "datasourceconfigexample": "Datasource configuration example", "taskid": "Ticket ID", "stepid": "Step ID", "stepname": "step name", "copytemplate": "Copy Template", "edittemplate": "Edit Template", "deletetemplate": "Delete Template", "addtemplate": "New Template", "conditionconfig": "Condition Configuration", "datasourceconfig": "Datasource Configuration", "contentconfig": "Content Configuration", "paramconfig": "Parameter Configuration", "isactive": "actived?", "visits": "visits", "control": "Control", "scheduledsending": "Scheduled sending", "editsendplan": "Edit send plan", "copysendplan": "Copy Send Plan", "newsendplan": "New send plan", "addsendplan": "Add Send Plan", "nextsendingtime": "Next sending time", "sendtimes": "Send Times", "sending": "Sending", "sendrecord": "Send Record", "canvassize": "<PERSON>vas Si<PERSON>", "alignguideline": "Alignment guideline", "selectedwidget": "Selected widget", "refreshrate": "refresh rate", "returnrows": "Return the number of rows (0 means unlimited)", "tilingstyle": "Tiling Style", "tiling": "tiling", "stretch": "<PERSON><PERSON><PERSON>", "borderspacing": "Border spacing", "cornersize": "Corner Size", "mainbordercolor": "Main border color", "subbordercolor": "subborder color", "motiondelay": "Motion Delay", "motionoffset": "Motion Offset", "coloralpha": "color alpha", "aolorbeta": "color beta", "highlightborder": "Highlight Border", "doublecoloroverlap": "Double Color Overlap", "rainbowfluorescence": "Rainbow Fluorescence", "displayrange": "Display Range", "country": "country", "province": "Province", "city": "city", "area": "area", "customcolor": "Custom Color", "randomcolor": "Random Color", "fontsize": "Character size", "smoothcurve": "Smooth Curve", "meter": "meter", "dialcolor": "Dial Color", "pointercolor": "Pointer Color", "centercolor": "Center Color", "datafontsize": "Data font size", "datacolor": "Data Color", "shape": "shape", "bubbleshape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bubblesize": "Bubble Size", "circle": "circle", "diamond": "Diamond Shape", "triangle": "triangle", "pin": "Teardrop", "rect": "square", "texturestyle": "texture style", "purecolor": "pure color", "dot": "dot", "line": "twill", "wavelength": "Wave Length", "innerradius": "Inner Radius", "horizontallayout": "Horizontal", "verticallayout": "Vertical", "datalayout": "Data layout", "statisticfontsize": "Statistics font size", "statisticcolor": "Statistics color", "displayarea": "Display area", "showtitle": "Show title", "titlecontent": "Title Content", "showexplain": "Show explanation", "explaincontent": "Explain content", "percentmode": "Percentage Mode", "showdata": "Show data", "autoscroll": "Auto Scroll", "example": "Example", "sourcefile": "Resource file", "positions": {"topleft": "top left", "top": "top center", "topright": "top right", "bottomleft": "Bottom Left", "bottom": "Center Bottom", "bottomright": "Bottom Right", "lefttop": "top left corner", "left": "Left Center", "leftbottom": "Bottom left corner", "righttop": "top right corner", "right": "Center Right", "rightbottom": "Bottom right corner", "inner": "Inner", "outer": "Outer", "spider": "Spider", "horizontalalign": "Horizontal Alignment", "autofit": "Adaptive Size"}, "axis": {"xcoordinate": "x coordinate", "xcoordinatetitle": "X coordinate title", "ycoordinate": "y coordinate", "ycoordinatetitle": "Y-coordinate title", "yaxisshow": "Show Y axis", "xaxisshow": "Show X axis", "yaxistitle": "Y-axis title", "xaxistitle": "X-axis title", "columnwidth": "Column width", "showlegend": "Show Legend", "legendposition": "Legend position", "legendlayout": "Legend layout", "yfieldalias": "Y-axis field alias", "yfield": "Y-axis field", "xfield": "X-axis field", "xfieldalias": "X-axis field alias", "colorfieldalias": "Color field alias", "sizefieldalias": "<PERSON><PERSON> field alias", "showleft": "Show Left", "showright": "Show Right", "xleftsetting": "Left X axis setting", "xsetting": "X axis setting", "ysetting": "Y-axis setting", "xrightsetting": "Right X-axis setting", "valueinterval": "value interval", "valuefontsize": "Value font size", "valueangle": "value angle", "tickline": "tick line", "xaxis": "X axis", "yaxis": "Y axis", "axiscolor": "Axis Color", "dividinglinedisplay": "Dividing line display", "verticaldividingline": "Vertical dividing line", "dividinglinecolor": "Dividing line color", "dividinglinewidth": "Dividing line width", "borderline": "Borderline", "valuealign": "Value Alignment", "axismarginsetting": "Axis Margin Setting", "marginleftright": "Left and right margins (pixels)", "marginleft": "Margin left (pixels)", "marginright": "Right margin (pixels)", "margintop": "Top margin (pixels)", "marginbottom": "Bottom margin (pixels)", "xaxisalias": "X-axis alias", "yaxisalias": "<PERSON><PERSON><PERSON> alias", "aliascolor": "Alias Color", "aliassize": "Alias size", "axisreverse": "Axis Reverse", "textAngleX": "Text Angle", "textspacing": "Text spacing", "scale": "scale", "equal": "equal", "leftequal": "Left equal", "rightequal": "right equal", "namefield": "Name Field", "colorfield": "Color field", "valuefield": "Value Field", "sizefield": "Size field"}, "fontweight": {"name": "Fontweight", "bold": "Bold", "bolder": "Extra Bold", "lighter": "lighter"}, "bubblesetting": {"name": "Bubble Settings", "fontminsize4pin": "Minimum Radius", "fontmaxsize4pin": "Maximum Radius"}, "position": {"left": "Left Margin", "top": "top margin", "widthplaceholder": "The width of the container in the 1920px large screen", "heightplaceholder": "The height of the container in the 1080px large screen"}, "datatype": {"name": "Data Type", "staticdata": "Static data", "bindstaticdata": "Static data binding", "binddynamicdata": "Dynamic Data Binding", "dynamicdata": "DynamicData", "refreshTime": "Refresh time (milliseconds)"}, "chartsetting": {"barlinechart": "Barline Chart", "barchart": "Histogram", "barstackchart": "Barstack Chart", "barcomparechart": "Bar comparison chart", "decoratepiechart": "Decoratepiechart", "staticdecoratepiechart": "Static Decoratepiechart", "funnelchart": "Funnel chart", "dashboard": "Dashboard", "heatmap": "Heatmap", "gradientbarchart": "Histogram-gradient color", "stackstyle": "Stackstyle", "stackleftright": "<PERSON>ack left and right", "stacktopbottom": "Stack top and bottom", "verticalshow": "Vertical show", "horizontalposition": "horizontal position", "verticalposition": "Vertical Position", "layoutFront": "Layout Front", "vertical": "Vertical", "horizontal": "horizontal", "value": "Value", "valuesetting": "value setting", "barvaluesetting": "bar value setting", "maxwidth": "Maximum width", "minheight": "Minimum height", "dynamicheight": "Dynamicheight", "transpose": "Transpose", "legendaction": "Legend Action", "legendname": "Legend name", "legendwidth": "Legend width", "brokenlinesetting": "Brokenline Setting", "markPoint": "Mark Point", "pointSize": "Point Size", "area": "Area accumulation", "areaThickness": "Area Thickness", "lineWidth": "Line Width", "barsetting": "Bar setting", "ringsetting": "Ring Setting", "gradient": "gradient color", "0to30percentgradient": "0%~30% gradient", "30to70percentgradient": "30%~70% gradient", "70to100percentgradient": "70%~100% gradient", "outerringsetting": "Outer ring setting", "outerquotaringsetting": "Outerquotaringsetting", "80percentringsetting": "Eight-point ring setting", "30percentringsetting": "Three-point ring setting", "ringouterringsetting": "Ringouterringsetting", "dashlinesetting": "Dashline setting", "insiderquotalinesetting": "Insiderquotalinesetting", "centerpiechartsetting": "Center pie chart setting", "dashlineamount": "Amount of dashed lines", "quota": "Indicator", "quotaline": "Indicator Line", "quotalineamount": "Amount of indicator lines", "quotalinelength": "Indicator line length", "quotalinewidth": "Indicator line width", "quotalinecolor": "Indicator line color", "showtickline": "Tickline display", "circleradius": "radius", "ticklineamount": "Amount of tick lines", "ticklinelength": "Tickline Length", "ticklinewidth": "Tickline width", "ticklinecolor": "Tickline Color", "100persentcolor": "100% color", "three1Color": "A section of color", "three2Color": "two color", "three3Color": "Three-segment color", "showring": "Ring Show", "ringcolor": "Ring Color", "ringwidth": "<PERSON>", "showquotaline": "Indicator line display", "shadowcolor": "Shadow Color", "shadowblur": "Blur factor", "crossstar": "crossstar", "Pentagram": "Pentagram", "hexagram": "hexagram", "reverse": "Reverse", "blocks": "blocks", "apple": "Apple", "samsung": "Samsung", "xiaomi": "<PERSON><PERSON>", "access": "Access", "consult": "Consult", "order": "Order", "click": "click", "show": "show", "colorat0": "0 color", "colorat0.5": "Color at 0.5", "colorat1": "1 color", "groupcolumnchart": "Grouped Column Chart", "columnchart": "column chart", "groupfield": "Group Field", "linechart": "Line Chart", "liquidchart": "Water Wave Chart", "areafill": "Area Fill", "stackedareachart": "Stacked area chart", "areachart": "Area Chart", "multilinechart": "Multiline Chart", "pscatter": "Scatter plot", "piechart": "Pie Chart", "classifyfield": "Classify Field", "pointchart": "floating point", "radarchart": "radar chart", "scatter": "Bubble Chart", "spark": "Fireworks", "stackedbarchart": "Stacked Bar Chart", "stackedcolumnchart": "Stacked Column Chart", "wordcloud": "word cloud map", "rosechart": "Rose chart", "bulletchar": "Bullet chart"}, "particle": {"motion": "Particle Motion", "color": "Particle Color", "opacity": "Particle Opacity", "count": "Count of Particles", "size": "Particle Size", "autoconnect": "Autoconnect", "connectdistance": "Connection Distance", "connectcolor": "Connection color", "connectopacity": "Connection Transparency", "connectwidth": "connection width"}, "describe": {"itemnamedescribe": "Support itemName* or *itemName two matching methods, if you enter *, it means match all objects.", "typedescribe": "Only objects within the matching scope of the whitelist can be accessed, and the blacklist is used to block objects in the whitelist.", "headerdescribe": "Header, optional, if it is empty, it means to display the name of the dataset used, and multiple headers are separated by English commas", "columndescribe": "Field, optional, empty to display all dataset data, multiple fields separated by commas", "widthdescribe": "width, default 1000", "heightdescribe": "Height, default 400", "showvaluedescribe": "false|true, whether to display the value on the column, the default is true", "tickdescribe": "number, when the x-coordinate display position is not enough, control the number of interval points", "reportisempty": "The report cannot be empty, please add a report", "choosetemplate": "Please choose a template", "least500": "Minimum 500", "least1000": "Minimum 1000", "templatetip": "Help: The template needs to conform to the Vue template syntax specification, and a root tag must be specified, usually a div, which supports all iView components. The data source results are returned in the form of an array, and the root attribute is: dataList. When accessing data, please directly Iterate dataList to get the required data.", "configtip": "Help: configuration is a piece of javascript code, which needs to define objects containing properties such as methods and computed.", "uploadtip": "Help: Only upload images are supported, reference image path example: <img src=\"{file:filename.png}\"/>", "inputtext": "Please enter text or bind data source"}, "district": "County-level administrative district", "firework": "Firework", "numberfield": "Number Field", "basicwidget": "Basic Widget", "otherwidget": "Other widgets", "movetop": "Move to top", "moveup": "Move up one layer", "movedown": "Move down one level", "movefloor": "Move to the bottom", "reportmanage": "Report Management", "templatemanage": "Template Management", "square": "square"}, "dashboard": {"dashboard": "Dashboard", "dashboardmanage": "Dashboard Management", "rangewidth": "Annular thickness", "systemdashboard": "System", "personaldashboard": "Personal", "widgetname": "Component Name", "widgetnameempty": "Component name cannot be empty!", "basicsetting": "Basic Settings", "datafilter": "Data filtering", "formatdisplay": "Format presentation", "nohyperlink": "No hyperlink temporarily", "conditionisnull": "The required condition for the presence of a presentation format is empty!", "conditionfail": "Conditional failure!", "sraechconditionempty": "There are empty search criteria!", "valuelisthasempty": "There is an empty result valueList!", "getwidgetfailed": "Failed to get dashboard components", "isdefaultdata": "Is the default data", "barchart": "Bar Chart", "editdashboard": "Dashboard Editing", "getwidgetdatafail": "Failed to obtain dashboard component data", "deletewidgetconfirm": "Are you sure to delete this component [{target}]?", "target": "Marking", "frontend": "prospect"}, "deploy": {"triggerstate": "Triggerstate", "triggerrange": "Trigger Range", "integratedaction": "Integrated Action", "appmodule": "application/module", "jobtype": "JobType", "integratedmanagement": "Integrated Management", "pipelinetype": "Pipeline Type", "allapplication": "All applications", "allmodule": "All modules", "functionoverview": "Function overview", "functiondesc": "When the release job of the specified application, module, and environment reaches the specified state (such as the completion of job execution), it will trigger the call to the external system interface. For example, when the release job of the UAT service is completed, it will automatically trigger the call to the PRD service interface and trigger Automatic publishing jobs for PRD environments.", "configmode": "configuration mode", "triggerconditionfilter": "Trigger condition filter", "triggerconditiondesc": "Applications, modules, and environment ranges can be specified, and the release job within the range will trigger the action; one or more job states can be specified, and the action will be triggered only when the release job flow reaches the specified state", "externalintegrationmode": "External call integration mode", "externalintegrationmodedesc": "Rely on the [Integration Management] function of this platform to call the external interface, so you need to pre-configure [Integration Management], and then refer to the corresponding integration configuration on the webhook configuration page. The configuration method is as follows: Configuration entry: in [System Configuration ] module, enter the [Integration Management] menu, click the [+Configuration] button in the upper left corner of the page to add integration", "integrationconfig": "Integration configuration: [Data Specification] needs to select [Publish Trigger Data Specification], [URL] [Request Settings] can be filled in according to the actual situation of the external interface", "owninguser": "Owning User", "groupuser": "Users in the same group", "otheruser": "Other user", "copyfile": "Copy File", "movefile": "Move File", "targetpath": "Target Path", "foldernamerepeat": "A folder with the same name exists", "filenamerepeat": "A file with the same name exists", "packagedownload": "Package download", "newfolder": "New folder", "uploadthefileandextractit": "Upload the file and extract it", "copythedownloadpackageaddress": "Copy the download package address", "lastrevisiontime": "Last revision time", "activeversion": "Active Version", "versionrollback": "Rollback from {oldversion} to {newversion}", "thelatestcodeproblem": "The latest code problem", "latestfifthcommentratestatistics": "The latest fifth comment rate statistics (%)", "thelatestfivecodestatistics": "The latest five code statistics", "lastcodetestresult": "The latest code test result", "lastfiveincrementalcoveragerate": "The latest five incremental coverage rates (%)", "lastfivefullcoveragerate": "The latest five full coverage", "codequalitytrend": "bug/vulnerability/code smell trend graph", "coderepeatability": "Repeatability Trend Chart", "massvalve": "Mass Valve", "totalproblem": "Total problem (blocking/critical/major)", "newaddproblem": "Add new problem (blocking/critical/major)", "bugs": "Overall Bugs", "newbugs": "New Bugs", "newreliabilityrating": "New reliabilityrating", "overallvulnerability": "Overall Vulnerability", "debt": "Debt", "newdebt": "New Debt", "maintainablerateofnewcode": "New code maintainable rate", "overallcodeflavor": "Overall code flavor", "overallsecurityhotspot": "Overall Security Hotspot", "newcodeodor": "New Code Odor", "newsecurityhotspot": "New security hotspot", "numberofnewlinesofcode": "Number of new lines of code", "totallinesofcode": "Total lines of code", "validlineofcode": "Validline of code", "linecommentrate": "line comment rate", "apicommentrate": "API comment rate", "ipname": "IP, name", "appnotsettingenv": "The current application has no configuration environment", "instancename": "InstanceName", "publishstatus": "Publish Status", "lastpublishtime": "Last publish time", "lastpublisheduser": "Last published user", "projectdirectory": "Project Directory", "successrate": "Success rate [success/failure]", "fullbranchcoverage": "Full branch coverage", "incrementalbranchcoverage": "Incremental branch coverage", "fulllinecoverage": "Full Line Coverage", "incrementalrowcoverage": "Incremental row coverage", "sealplate": "Sealplate", "selectaddversion": "Please select an application or module before creating a version", "noconfigauthtip": "You do not have the \"edit configuration permission\" for the current application, please contact the administrator for authorization", "notversionproductauth": "You do not have version & product management rights, please contact the administrator for authorization", "failurecount": "(Failed {target} times)", "flowchart": "Flowchart", "discarddraft": "Discard draft", "editdraft": "Edit Draft", "applicationsystem": "Application System", "nosavedraft": "You have an unsaved draft, please choose to continue editing the draft or discard the draft and edit again", "userhasnosavedraft": "User {username}/{userid} has an unsaved draft, please choose to continue editing the draft or discard the draft and edit again", "chooseatleastonephase": "Choose at least one phase", "chooseatleastonescenario": "Choose at least one scenario", "phaseatleastonetool": "Phase {target} settings: select at least one tool", "phasesetcondition": "Phase {stepname} setting [{operationname}] please enter the condition", "phasesetinputparamtip": "Phase {stepname} setting: [{operationname}] input parameter data is incomplete", "phasesetfreeparamtip": "Phase {stepname} setting: [{operationname}] free parameter data is incomplete", "phaseselectpresetparamtip": "Phase {stepname} setting: 【{operationname}】Please select a preset parameter set", "phasetooldeletetip": "Phase {stepname} tool [{operationname}] has been deleted, please clean up the tool and save it", "noappviewauth": "You do not have configuration view permission for [{abbrname} app]", "scenesetting": "Scene Setting", "selectiontool": "Selection Tool", "actuator": "Actuator", "copyconfig": "Copy Configuration", "deleteenv": "Delete Environment", "noapplytip": "The system has not added an application, click", "applynotconfigselect": "The current application has not yet added configuration, please select", "pipelinetemplate": "Pipeline template", "addatleastonescene": "Add at least one scene", "executivestrategy": "Execution Strategy", "presettarget": "Preset Target", "executetargetdesc": "Divide the execution target into N batches according to the number, and execute them successively. The batch number set by the stage group has a higher priority than the batch number set in the combination tool or job", "deleteallcite": "When editing the English name of a parameter or deleting a parameter, all citations will be released. Do you want to continue?", "referencequery": "Reference query", "noparamreference": "No parameter reference yet", "upstreamanddownstreamparametervalues": "Upstream and Downstream Parameter Values", "upstreamanddownstreamparametersets": "Upstream and Downstream Parametersets", "presetexecutiontarget": "Preset execution target", "editphasetarget": "Editphasetarget: {target}", "viewphasetarget": "View Phase: {target}", "dragtochangetheorder": "Drag to change the order", "applynoaddmoduletip": "The current application does not add modules, no need to set", "operationauth": "Operation Authorization", "envauth": "Environment Permissions", "scenarioauth": "Scene Authorization", "pleaseselectatleastonepermission": "Please select at least one permission", "batcheditpermission": "Batch edit permission", "hierarchyswitching": "Hierarchy Switching", "existdisablephase": "Disable phase exists", "existheavyloadphase": "Existheavyloadphase", "deleteallauthconfirm": "Confirm to delete all permissions for everyone?", "deleteallauthconfirmtarget": "Confirm to delete all permissions of {target}?", "batchdeleteselectedauthconfirm": "Batch delete selected authconfirm?", "useofstate": "Use of state", "displaysystemname": "Display system name", "abbreviation": "abbreviation", "superpipeline": "Superpipeline", "joblist": "Job List", "batchjob": "<PERSON><PERSON>", "copyenv": "Copy Environment", "copystrategy": "Copy Strategy", "copytotheexistingenvironment": "Copy to the existing environment", "targetenvironment": "Target Environment", "notconfigenvdesc": "Only supports copying environment configuration information to unconfigured or cleared configuration environments", "adaptationfileadaptation": "Adaptationfileadaptation", "casedifference": "Case Difference", "notconfigfiletip": "No configuration file adaptation, click", "configfilecasedifference": "Configuration File Case Difference", "notconfigdifftip": "Instance diff not configured, click", "notaddexampletip": "Not added instance, click", "dbconfig": "DB Configuration", "bindaccount": "Bind account", "databaseschema": "databaseschema", "inputformatisnotstandardpleaseenter": "The input format is not standard, please enter: dbname.username", "thereisoneandonlyonedot": "dbname.username, there is and only one dot", "schemaformatdesc": "schema input format: dbname.username", "customparameter": "Custom parameter", "connectiontimeout": "Connection timeout (seconds)", "autocommit": "Autocommit", "dbversion": "DB version", "ignoreerrors": "ignore errors", "dbarole": "DBA role", "selectexistexample": "Select an existing instance", "configurationmodel": "Configuration Model", "selectexample": "Select an instance", "maintenancewindow": "Maintenance Window", "versionprefix": "Version Prefix", "versionprefixdesc": "Specify the prefix of the version to be created, you can leave it blank", "interceptionrule": "Interception rule", "versionruledesc": "Regular expression, specify which part of the branch to use as the main version, if not given or the regular expression fails to match, the branch name will be used. Legal regular expressions must contain pairs (,), for example: develop(\\d+(\\.\\d+)*\\.\\d+) extract 1.2.3 in the develop1.2.3 branch as the main version number", "splicingcommitid": "splicing commitID", "splicingcommitiddesc": "Splicing the commit ID of the latest commit in the version number", "unspliced": "Unspliced", "splicing": "splicing", "downloadscript": "Download script", "gitdesc": "After the code warehouse merges the code (such as GitLab's post-receive event, SVN's post-commit event), call this system interface to create a release job, or create a release batch job.", "gitlabconfigmode": "GitLab warehouse configuration mode", "writewarehouseinfo": "Fill in warehouse information", "writewarehouseinfodesc": "Warehouse information needs to be filled in, including GitLab service address, user name, password, warehouse name, branch information", "writesvnwarehouseinfodesc": "Warehouse information needs to be filled in, including SVN service address, warehouse name, and branch information", "actionconfig": "Action Configuration", "actionconfigdesc": "Binding actions, such as creating jobs, creating batch jobs, and filling in action parameters, such as the version number of publishing jobs, etc.", "gitlabcreatehook": "GitLab Create Hook", "gitlabcreatehookdesc": "The system will call the GitLab interface based on the warehouse information in step 1, and automatically create a Hook triggered by the post-receive event within the specified branch range", "takeeffect": "take effect", "takeeffectdesc": "After the configuration is complete, when a post-receive event occurs in the warehouse and branch specified in step 1, GitLab will access this system through the interface, and create a release job in this system according to the job parameters in step 2", "svnwarehouseconfig": "SVN warehouse configuration method", "svncreatehook": "SVN Create Hook", "svnwarehousesetting": "SVN warehouse, users need to log in to the SVN server and modify the Hook script", "enterhooksdirectory": "Enter the hooks directory", "createpostcommit": "Create a post-commit file based on the post-commit template file and authorize execution", "updatepostcommit": "Modify the post-commit script and call the system to create a job interface in the script (you need to modify the interface address of the DEPLOYURL parameter in the script below)", "notautodeploynotcontinue": "If there is no --autodeploy in the submission information, do not perform subsequent continuous integration actions", "formatteddata": "Formatted data", "svntakeeffectdesc": "After the configuration is complete, when a post-commit event occurs in the warehouse and branch specified in step 1, SVN will execute the post-commit script and call the interface of this system. According to the job parameters in step 2, in this The system creates a publish job", "postcommitscript": "post-commit script.txt", "configurationlist": "Configuration List", "warehouse": "warehouse", "warehousetype": "warehouse type", "warehousename": "warehouse name", "warehouseserveraddress": "Warehouse server address", "serviceaddress": "Service Address", "branchactiondesc": "Trigger an integration action when this event occurs on a branch", "actiontype": "action type", "actiontypedesc": "After the action is modified, the configuration information corresponding to the action will be cleared", "delaytime(s)": "Delay time(s)", "updatewarehousewillbedelete": "If the warehouse server address or warehouse name is modified, the gitlab webhook of the original warehouse will be deleted", "branchnamewildcardcharactersaresupported": "Branch name, wildcard characters are supported", "branchtriggerintegrationdesc": "Events on this branch will trigger integration, if not filled all branches will trigger", "branchchangedesc": "The name of the branch under the warehouse. The change of this branch content will trigger continuous integration. If you fill in \"/\", any branch change of repo will trigger continuous integration. Wildcards are supported", "reponamedesc": "Example: projects/moduleName", "addressexample": "Example: /www/svn/demo_repo", "warehousecompletepath": "The complete path name of the warehouse name, example: /home/<USER>/svn/repoName", "branchexample": "Example: branches/v1.0.0", "branchfiltersvntooltip1": "1, *: match any number of characters in a single-level directory.", "branchfiltersvntooltip2": "eg /Projects/Branches/* matches /Projects/Branches/v1.2.3/ but not /Projects/Branches/v1.2.3/Java/", "branchfiltersvntooltip3": "2, **: match any number of characters.", "branchfiltersvntooltip4": "such as /Projects/Branches/** matches /Projects/Branches/v1.2.3/, /Projects/Branches/v1.2.3/Java/ and /Projects/Branches/v1.2.3/Java/Spring/", "branchfiltersvntooltip5": "3, ?: match a single character.", "branchfiltersvntooltip6": "eg /Projects/Branches/v?.?.? matches /Projects/Branches/v1.2.3/ but not /Projects/Branches/v1.2.3.4/", "branchfiltersvntooltip7": "4, {pattern1, pattern2,...patternN}: only match the given pattern.", "branchfiltersvntooltip8": "For example, /Projects/Branches/{v1.2.3, v1.2.4, v1.2.5} only matches v1.2.3, v1.2.4, v1.2.5 under /Projects/Branches/", "branchfiltersvntooltip9": "5. The square bracket pattern specifies a single matching character set.", "branchfiltersvntooltip10": "For example [0-9] matches a single number, [a-z, A-Z] matches any letter, /Projects/Branches/v[0-9.]* matches /Projects/Branches/v1.2.3/, / Projects/Branches/v1.2.3.4/", "branchfiltersvntooltip11": "6. To match special characters, use \"\\\" to escape, such as /Projects/Branches/v\\[ matches /Projects/Branches/v[/", "commitid": "Commit ID", "associatedevent": "Associated Event", "notsceneauth": "You do not have [{target}] scene permission, please contact the administrator for authorization", "notenvauth": "You do not have [{target}] environment permission, please contact the administrator for authorization", "envnotexample": "The module has no instance in the {target} environment", "instancefiltering": "Instance Filtering", "copymodule": "<PERSON><PERSON>", "copytoexistingmodule": "Copy to existing module", "objectmodule": "object module", "objectmoduledesc": "Only supports copying module configuration information to unconfigured or cleared modules", "configrunnergroup": "Configure Runner group", "runnergroup": "Runner group", "applicationinformation": "Application Information", "moduleinformation": "Module Information", "continuousintegration": "Continuous Integration", "currentoperationwillclear": "The current operation will clear:", "currentpipelineconfig": "1) Current application pipeline configuration", "currentappauthconfig": "2) Current app authorization configuration", "allmoduleconfig": "3) All module configuration", "allenvconfig": "4) All environment configurations", "deleteconfirm": "And it does not support undo after cleaning, it needs to be reconfigured, do you want to continue?", "currentmoduledatareset": "1) Pipeline-related reload data of the current module", "currentmoduleexecuteconfig": "2) Executor configuration for the current module", "currentmoduleallenvconfig": "3) Configuration of all environments under the current module", "moduleenvdeleteconfirm": "And after cleaning, the current module and all the environment of the module will inherit the application configuration; after cleaning, undo is not supported, continue?", "currentenvdatareset": "1) The relevant reload data of the pipeline in the current environment", "currentenvfileconfig": "2) The configuration file adaptation data of the current environment", "currentenvdbconfig": "3) DB configuration of the current environment", "envdeleteconfirm": "And after cleaning, the current environment configuration will inherit the module configuration; undo is not supported after cleaning, continue? ", "configurationfileadaptation": "Configuration file adaptation", "instancelist": "Instance List", "actuatorgroupallocation": "Actuator Group Allocation", "modulenotconfigrunnergroup": "The current module is not configured with a runner group, which will cause the publishing job to fail to run. Click", "actuatorgroup": "Actuator Group", "associatedrunner": "Associated runner", "connectionmode": "Connection Mode", "commandport": "command port", "heartbeatport": "Heartbeat Port", "chooseatleastonetool": "Choose at least one tool", "currentmodulenotadddbconfig": "DB configuration has not been added to the current module, click", "compiletime": "Compile time", "addbuildno": "New BuildNo", "selectbuildno": "Select BuildNo", "sealedplate": "(sealed plate)", "unsealedversion": "(unsealed version)", "selectversion": "Select Version", "moduleenvnotinstance": "{modulename} module has no instance in {envname} environment", "currentmodulenotsetexecutetip": "The current module does not have an executor configured, and the job cannot be created,", "clickadd": "Click to add", "atleastselectamodule": "Module settings: select at least one module", "moduleconfigselectversion": "Module settings: 【{target}】Please select a version", "moduleconfigatleastselectinstance": "Module configuration: [{target}] select at least one instance", "createsuccess": "Create successfully", "createfail": "Failed to create:", "appmodulecreatesuccess": "The application module {target} was created successfully,", "clickredirect": "Please click to redirect", "applymodule": "Apply Module", "currentapplynoconfig": "The current application has not been configured, click", "applynoconfigmodule": "The current application does not configure any modules, click", "noconfigenv": "Environment information is not configured, click", "cancelwait": "Cancel wait", "forceunlock": "Force Unlock", "lockingoperation": "Locking operation", "lockingduration": "Locking duration", "locktime": "Lock Time", "jobnotexecutefinish": "Job {target} has not completed execution,", "jobexecutionmayfailconfirm": "May cause job execution to fail, whether to continue", "addbatchjob": "Add batch job", "creationmode": "Creation Mode", "directcreation": "Direct Creation", "jobauthorization": "Job Authorization", "authorizeduser": "Authorized User", "authorizedusercanexecutebatchjob": "Authorized users can execute the current batch job", "rejectbatchjobconfirm": "Confirm to reject the current batch job?", "passbatchjobconfirm": "Confirm to pass the current batch job?", "batchchannel": "Batch Channel", "tasktimelimitfiveminutes": "In order to prevent the task from expiring immediately after submission, it is only allowed to select a time after 5 minutes", "manualtrigger": "Manual Trigger", "automaticexecution": "Automatic execution", "atleastaddajob": "Please add at least one job", "deletebatchjobconfirm": "Confirm to delete the current batch job?", "automaticallyexecutesubsequentgroups": "Automaticallyexecutesubsequentgroups", "automaticexecutegroupdesc": "Whether to automatically execute subsequent groups after all jobs under the current group are executed", "batchjobexecutestrategy": "Batch job execution strategy", "skipallfinishedchildjob": "<PERSON><PERSON> all completed child jobs", "skipfinishedignorenode": "Skip all completed, ignored nodes", "executeallchildjob": "Execute all child jobs", "childjobexecutestrategy": "Child job execution strategy", "executeallnode": "Execute all nodes", "noselecttarget": "Currently no selectable {target}", "roundcountvalidate": "Batch settings: please select or enter the correct number", "jobparamvalid": "Job parameters: the data is incomplete or wrong", "noconfigenvauth": "You have not configured \"Environment Permission\", click", "noconfigscenauth": "You have not configured \"scenarios\", click", "pleaseselectmoduleenvaddjob": "Please select an application or module before creating a job", "phaseexistignorenode": "There is an execution node with status Ignored in the phase", "pleaseselecttimeperiod": "Please select a time period", "executor": "executor", "selectjob": "Select Job", "jobtemplate": "Job Template", "envscene": "Environment (scene)", "sealededition": "(sealed edition)", "currentapplynotconfig": "The current apply is not configured", "pleaseselectapply": "Please select apply first", "atleastaapplymoduleandwriteversion": "Select at least one application module and write version", "basicinfonotwaritecompletevalid": "The current basic information is not complete", "notapplyeditconfigauth": "You do not have permission to edit configuration for the current application", "notapplyallsceneexecuteauth": "You do not have the execute permission to apply all scenes", "notapplyallenveexecuteauth": "You do not have the execute permission to apply all environments", "applynotconfigpipeline": "The current application does not configure a pipeline", "applynotconfigmodule": "No module is currently applied", "applynotconfigenv": "The current application does not configure the environment", "modulenotconfigenv": "The current module does not have an environment configured", "codechange": "Code change", "cveloophole": "CVE vulnerability", "lastsynchronizationtime": "Last synchronization time", "fileaddcount": "Number of file additions", "filemodifycount": "Number of file modifications", "filedeletecount": "Number of file deletions", "lineaddcount": "Number of code lines added", "linedeletecount": "Number of code lines reduced", "importpipelineconfig": "Import assembly line configuration", "coverpipeline": "Coverage assembly line", "cancelimport": "Cancel Import", "relateobjectcover": "Associated Object Overrides", "overrelateobjectdescription": "The following objects associated with the assembly line already exist in the system. To overwrite them, please check the objects that need to be overwritten and click the 'Import' button below to complete the import.", "overexistpipelineiscontinue": "The pipeline has been configured. Importing the pipeline will overwrite the existing pipeline. Do you want to continue?", "issuecount": "Number of requirements", "scenariosteperror": "All stages in the scene are disabled. The scene must have at least one non disabled stage", "deletecodeline": "Delete Code Line", "addcodeline": "Add Code Line", "deletefilecount": "Number of deleted files", "modifyfilecount": "Number of modified files", "addfilecount": "Number of newly added files", "withoutconfigandeditauth": "The current application has not been configured yet. Please contact the publishing administrator to authorize the configuration", "withoutconfigeditauth": "Please contact the publishing administrator for authorization configuration", "envattr": "Environmental attributes", "addenvattrtips": "Unconfigured attributes, click", "noconfigviewauthtip": "You do not have the \"View Configuration Permission\" for the current application. Please contact the administrator for authorization", "pleasechoosedatabase": "Please select a database first", "currentenvbuildno": "{target} environment BuildNo", "buildnopending": "To be built", "actuatorgrouptag": "Actuator group label", "blueset": "Blue green setting", "blueSet": "Blue Green", "ismodified": "Modified", "presetrunnergroup": "Preset actuator group", "testrunnerdesc": "If not specified, actuators will be automatically assigned based on IP matching of actuator groups according to network segments", "jobcount": "Number of published assignments", "jobrecord": "Publish homework records", "notapplyallexecuteauth": "You do not have the execution permission for the current application"}, "inspect": {"alarmprompt": "Alarm Prompt", "alarmobjectvalue": "Alarm object value", "alarmobject": "Alarm Object", "ruleid": "Rule ID", "rulename": "Rule Name", "applicationofrule": "The application to which the rule belongs", "sendanemail": "Send Mail", "latestproblemofinspectiontarget": "Inspect latest problem {target}", "sendmaillimitdesc": "Send emails to up to 100 people at a time, and emails exceeding 100 will not be sent", "inputtargetnameip": "Please enter the target name, ip", "thresholdrule": "<PERSON><PERSON><PERSON><PERSON> Rule", "jobdetail": "Job Details", "monitoringstate": "Monitoring state", "inspectionjobstatus": "Inspection job status", "iplist": "IP list", "addrule": "Add Rule", "inheritglobal": "Inherit Global", "thressholdrulealert": "Select the target application below to overwrite the threshold rules of the selected application with the personalized threshold rules of the current application.", "selectapp": "Select at least one app", "assetthresholdrule": "Current asset threshold rules:", "globalthresholdrule": "Global Threshold Rule", "inspect": "On-Site Inspection", "appinspect": "Application Patrol", "noconfig": "The associated configuration item is not found in the current environment. Please edit the relevant configuration item and fill in the [Application Environment] information", "inspecttool": "Patrol tools", "inspectmodule": "Patrol module", "clustername": "Cluster", "envname": "application environment ", "datacenter": "Data Center", "selecttargetwarning": "Please check at least one type of patrol target before executing", "scheduleinspect": "Regular patrol inspection", "newproblem": "Latest issues", "assetlist": "Asset List", "moduleinspect": "Module Patrol", "noenvconfig": "The current module (or application) is not configured with environment information", "modulenoasset": "No associated assets found in the current module", "selectenv": "Please select at least one environment", "appnoasset": "The current application did not find associated assets", "selectmodule": "Please select at least one module", "allenv": "ALL", "noconfigenv": "Unconfigured", "exeount": "Executed after startup", "jobexerecord": "Job Execution Record", "scriptmanage": "Script Management", "urlconfig": "Configure URL Dial Test", "scriptlist": "Script List", "expandconfig": "Expansion configuration", "lookingthroughreport": "You are browsing the historical report", "historyreport": "Historical Reports", "batchinspect": "Batch inspection", "connectionagreement": "Connection Agreement", "inputmoduletypename": "Fill in the model type name or the IP (name) of a single host, which cannot be edited", "assetinspectconfirm": "Are you sure to perform a patrol on the resource<b>{target}</b>?", "inspecttoolis": "The patrol tool is:", "inspectcondition": "Batch inspection of all assets that meet the following conditions", "noconfigtool": "The resource {target}</b>is not configured with a patrol tool and cannot be patrolled.", "clicktoconfig": "Click Configure", "inspectexec": "Perform Patrol Inspection", "inputreturn": "Wrapping multiple paths", "selectedasset": "Assets checked in the table", "assetfilter": "All assets under current filter conditions", "unselectedasset": "Assets not checked in the table", "clearfile": "Clean up files", "configpath": "Configuration Path", "filelist": "File List", "filechangetime": "File modification time", "changerecord": "Change Record", "scanhistory": "Scan History", "latestchangetime": "Latest change time", "searchplaceholder": "File name, asset IP, asset name", "assetip": "Asset IP", "assetname": "Asset Name", "assettype": "Asset Type", "pathconfig": "Path Configuration", "batchclearfile": "Batch Cleanup Files", "clearcofigfilerecord": "Clean up all {target} configuration file records", "exporttargetrule": "Export metrics and rules", "targetfilter": "Indicator filtering", "inspecttoolsetting": "Inspection tool settings", "deleteglobalruleconfirm": "Deleting global rules will automatically delete rules with the same ID in the application rules. Do you want to continue?", "inspecttoolmanage": "Management of inspection tools", "selecttargetatleast": "Select at least one indicator", "cancelselectedtargetconfirm": "Canceling the collection of a certain indicator will result in the invalidation of the threshold rule containing that indicator. Do you want to continue?", "inspectresult": "Inspection results", "problemreport": "Problem report", "ruletooltips": "For example, the rule for the usage rate of mount points starting with<br/>/dev to be greater than 90% is: $. MONT_ POINTS [NAME Startswith/dev]. USED%>90, attribute matching rules support operation symbols:>,<,>=,<=,==,!=, The rule for process CPU usage exceeding 50% is: $. TOP_ CPU_ RPOCESSES.CPU_ USAGE {$this/$. CPU_LOGIC-CORES}>50, the final brace calculates the obtained attribute value and compares it with 50, where $this represents the attribute CPU_ Value of USAGE", "resourcetypenotsetinspecttooltiptarget": "Resource type {target} is not configured with a patrol tool and cannot be patrolled."}, "pbc": {"importclassidentifier": "Import classification ID", "edittype": "Edit Type", "addtype": "Add Type", "levelidentifier": "Level identifier", "parenttype": "Parent Type", "datareport": "Data reporting", "paramconfig": "Parameter settings", "dataelementname": "Data element name", "dataelementtransferid": "Data element transmission identification", "firstclassidentifier": "Primary classification identifier", "secondclass": "Secondary classification", "secondclassidentifier": "Secondary classification identifier", "thirdclass": "Three level classification", "thirdclassidentifier": "Three-level classification identifier", "fourthclass": "Four level classification", "fourthclassidentifier": "4-level classification identifier", "classidentifier": "Classification identifier", "conform": "Whether the submission meets the requirements", "organization": "Organization", "financialorganizationcode": "Financial Institution Code", "editorganization": "Editorial organization", "addorganization": "Add Organization", "codedescribe": "Refer to the 14-digit code defined in the \"Financial Institution Code Specification\" issued by the People's Bank of China", "loginurl": "Certified Address", "reporturl": "Reporting data address", "validurl": "Application check address", "validresulturl": "Query and check result address", "authtype": "Certification type", "clientid": "Client ID", "clientpassword": "Client Password", "addinterface": "Add Interface", "interfaceid": "Interface identification", "interfacename": "Interface Name", "importinterfacedefine": "Import interface definition", "importintefacewarning": "Warning: Re-importing interface definitions will clear all attribute related configurations. Please operate with caution.", "importinterfacedata": "Import interface data", "interfacedeleteconfirm": "Are you sure to delete the interface {target} and all its attribute configurations?", "editproperty": "Edit Properties", "propertytransferid": "Attribute transfer identifier", "propertytransfername": "Attribute Transfer Name", "complexid": "Composite attribute transfer identifier", "complextransfername": "Composite Attribute Transfer Name", "propertyaliasdescribe": "This field can be used as the header name when exporting data. If it is not set, the original name will be used", "valuerange": "range", "addrelation": "Add Relationship", "editrelation": "Edit Relationship", "valueproperty": "Value attribute", "importdata": "Import Data", "exportdata": "Export Data", "noorganization": "There is no organization configuration, please <a href=\"javascript:void(0)\" @click=\"$router.push({ path: '/corporation-manage' })\">add it</a> first", "chooseinterface": "Please select an interface", "deletedataconfirm": "Are you sure to delete the selected data?", "editdata": "Edit Data", "adddata": "Add Data", "adddataitem": "Add Data Item", "mappingconfig": "Mapping Configuration", "importinterface": "Import Interface", "exporttemplate": "Export Template", "restraintcondition": "constraint condition", "datatransferid": "Collection data element transmission identification", "defaultvaluedesc": "Use this value instead when the attribute is empty", "convertvaluedesc": "Format: Original value: New value, multiple configurations separated by commas", "attributemappingsetting": "Attribute Mapping Settings", "ruletip": "Help: An organization will only import configuration items that meet its own rules. If the organization does not configure rules, it means that all configuration items in the model will be associated with the organization.", "choosemodel": "Please select a model or view first", "modelattribute": "Model Properties", "uploaddataamount": "<b class=\"text-primary\">{target}</b> pieces of data need to be reported", "batchid": "Batch ID", "message": "information", "classifyidentifier": "Classification identification", "dataamount": "Data", "groupid": "Group ID", "initiationmethod": "Initiation method", "restartinbreak": "Re-execute from the interrupted location", "restartexecute": "Execute again from scratch", "selectedinterface": "Selected interface", "gatherinterface": "Acquisition interface", "editpolicy": "Edit Policy", "addpolicy": "Add Policy", "timeplan": "Time Schedule", "relevanceinterface": "Associated interface", "chooserelevanceinterface": "Please select at least one associated interface", "viewexecutionrecord": "Execution records", "interfaceamount": "Interface count", "cromexpression": "Schedule", "lastexecutertime": "Last Execution Time", "deletepolicyconfirm": "Are you sure to delete the policy: {target}?", "exepolicyconfirm": "Determine the execution strategy: {target}?", "uniquekeydesc": "The system automatically generates a unique identifier", "addenum": "Add Members", "editenum": "Edit Enumeration Member", "addproperty": "Add attribute", "propertyname": "Attribute Name", "complexname": "Compound Attribute Name", "restraint": "constraint", "enumitem": "Enumerate Members", "deletepropertyconfirm": "Are you sure to delete the attribute: {target}?", "selectdataurl": "Query data processing status address", "errorcode": "Error code", "facilitycategory": "Data element classification identifier", "facilitydescriptor": "Data element facility identifier"}, "rdm": {"project": "project", "field": "Field Definition", "projectstatus": "Project Status", "objectstatus": "Object Status", "projectinfo": "Project Information", "projectsets": "Project Settings", "statussets": "Status Settings", "objectsets": "Object Settings", "customattribute": "Custom Properties", "systemattribute": "System Properties", "initstatus": "Initial state", "buildproject": "Create Project", "isstart": "Start status or not", "isend": "End status or not", "goto": "Transferred to", "parentnode": "Parent node", "request": "demand", "appsets": "Application Settings", "attributesetting": "Property settings", "isrequiredwhencreate": "Required when creating", "nosetnolimit": "No setting represents no restriction", "requiredattribute": "Required attribute", "targetstatus": "Reachable state", "statusrel": "State relationship", "notgoto": "Do not transfer to", "daterange": "Date Range", "datetimerange": "Date Time Range", "inputtype": "Input Type", "deleteprojectdesc": "Permanently delete this item. This operation is irreversible, please be cautious.", "deletecurrentprojectdesc": "Are you sure to delete the current project? This operation is irreversible, please operate with caution.", "deletepropertydesc": "Are you sure to delete the current attribute? Note: After deleting an attribute, its value will also be deleted and cannot be restored.", "projectname": "entry name", "projecttype": "Project Type", "managerdesc": "The project leader can manage project related information", "projectmember": "Project members", "startenddate": "Starting and ending dates", "peojectdesc": "Project Description", "childrequest": "Sub requirement", "nextstatus": "Next state", "auditlist": "Change History", "bug": "defect", "requestmanage": "demand management ", "disconnect": "Disconnection", "relativerequest": "Related Requirements", "attrsetting": "Field Settings", "taskmanage": "task management", "iterationmanage": "Iterative management", "bugmanage": "defect management ", "iteration": "iteration", "isopened": "Opened", "isclosed": "Closed", "startdate": "Expected start", "enddate": "Expected end", "testcase": "test case", "templatesetting": "Template Settings", "testcondition": "Preconditions", "teststep": "Use Case Steps", "testresult": "Expected results", "pleasesetstatus": "Please define the status first", "listview": "List View", "levelview": "hierarchy view ", "changetime": "Change time", "editor": "Changed by", "changetype": "Change method", "changeattr": "Change attributes", "beforechange": "Before change", "afterchange": "After the change", "noiteration": "There are currently no iterations", "selectedphase": "Selected stage", "stopsecret": "Deactivate Secret", "enablesecret": "Enable Secret", "pleasecreatewebhookurl": "Please generate WebhookUrl", "copycommitword": "Copy Source Keyword", "gitlabcommit": "Git<PERSON>b submission", "copyissueid": "Copy ID", "projectmanager": "Project leader", "new": "new", "pleaseselectrole": "Please check the user roles that need to be deleted", "modulelist": "Module List", "projectisdeleted": "The current project has been deleted", "errortip": "Abnormal prompt", "noauthforissue": "You are not a member of the current project and do not have permission to access the current project.", "apply": "application", "joinproject": "Join this project.", "dburl": "Database Link", "dragapp": "Drag activated apps to sort", "saveastemplate": "Save as Template", "saveprojectastemplate": "Save the current project configuration as a project template.", "noauthforeditproject": "You are not the administrator or owner of the current project and cannot edit the project", "endproject": "End Project", "closeprojectdesc": "Closed projects can only be reopened by administrators", "projecttemplate": "Project Template", "isclose": "Whether to close", "openproject": "Open Project", "applist": "Application List", "relativetestcase": "Associated Use Cases", "repository": "Version Library", "gantt": "Gantt chart Chart", "noconfig": "This component does not require configuration", "cost": "spend", "costdate": "Spending date", "costtime": "Labor hours spent", "usedtimecost": "Labor hours used", "editstartenddate": "Edit Schedule Time", "plantimecost": "Estimated working hours", "exceedtimecost": "Exceeding work hours", "used": "Used", "exceed": "go beyond", "testplan": "test plan", "statusreluser": "The system automatically sets the handler during status flow", "innerparam": "Internal variables", "storywall": "Story Wall", "forbiddentransfer": "Cannot be circulated", "importtestcase": "Import test cases", "downloadtemplate": "Click to download and import the template", "pleaseactiveapp": "Please activate at least one app"}, "plugin": {"65535": "An integer between 0 and 65535", "stepscount": "Step %0 of %1", "aquamarine": "Sea Blue", "blockquote": "Block quote", "breaktext": "Text Break", "bulletedlist": "Bulleted List", "captionforimg": "Picture caption:", "captionforimgtarget": "Picture description: %0", "centeredpic": "picture centering", "changeimgtotext": "Change Picture Replace Text", "titletype": "Title Type", "decreaseindent": "decrease indent", "deletecolumn": "Delete this column", "deleterow": "Delete this line", "dimgrey": "Dark gray", "downloadable": "Downloadable", "dropdowntoolbar": "Dropdown toolbar", "editblock": "Edit Box", "editlink": "Modify Link", "contenttoolbar": "Editor Block Content Toolbar", "contextualtoolbar": "Editor <PERSON><PERSON>", "editingarea": "Editor editing area: %0", "editortoolbar": "Editor toolbar ", "fullsizeimg": "Full size image", "headercolumn": "Title Column", "headerrow": "Title Line", "headertarget": "Title {target}", "imgtoolbar": "Picture toolbar", "imgwidget": "Picture component", "inline": "Hanlin Academician", "increaseindent": "Increase Indent", "insertcolumnleft": "Left Insert Column", "insertcolumnright": "Right Insert Column", "insertimg": "insert images", "insertimgorfile": "Insert Picture or File", "insertmedia": "Insert Media", "insertparagraphafter": "Insert paragraph after", "insertparagraphbefore": "Insert paragraph before", "insertrowabove": "Insert a line above", "insertrowbelow": "Insert a row below", "inserttable": "Insert Table", "leftalignedimg": "Align Image Left", "lightblue": "light blue", "lightgreen": "Light green", "lightgrey": "Light gray", "linkurl": "Link URL", "mediaurl": "Media URL", "mediawidget": "Media widget", "mergecelldown": "Merge Cells Down", "mergecellleft": "Merge Cells Left", "mergecellright": "Merge Cells Right", "mergecellup": "Merge Cells Up", "mergecell": "merge cell", "numberlist": "Project Number List", "openfilemanager": "Open File Manager", "openinnewtab": "Open in new tab", "openlinkinnewtab": "Open link in new tab", "openmediainnewtab": "Open media in new tab", "pastemediaurl": "Paste media URL in input", "richtexteditor": "Rich Text Editor ", "rightalignedimg": "Align image to the right", "selectcolumn": "Select Column", "selectrow": "Select Rows", "showmore": "Show more", "sideimg": "Image side display", "splitcellhori": "Split Cells Horizontally", "splitecellverti": "Split Cells Vertically", "tabletoolbar": "Table toolbar", "textalternative": "replace text", "turquoise": "<PERSON><PERSON>", "unlink": "Unlink", "uploading": "Uploading", "widgettoolbar": "Widget toolbar", "wraptext": "text wrapping ", "correcttime": "Correct time", "intgreaterthan0": "An integer greater than or equal to 0", "floatpointnumber": "Floating point number", "smtpserver": "SMTP server address", "popserver": "POP server address", "imapserver": "IMAP server address", "ipadressandport": "IP address and port", "positivenumberor-1": "-1 or positive number", "adress": "Reserved Address", "mask": "Mask", "nonnegativeint": "Non negative integer", "int": "integer", "alphabetornumber": "English letters or numbers", "alphabetnumberempty": "English letters or numbers, spaces allowed", "capalphabet": "English uppercase letters", "loweralphabet": "English lowercase letters", "1to12": "An integer between 1 and 12", "properlyformattestring": "Correctly formatted string"}, "codehub": {"recentlyupdate": "Latest updates", "proxyaddress": "Proxy address", "proxyaddressdesc": "Deployed on the same machine as the codebase service to create an SVN repository", "agentusername": "Proxy username", "agentpassword": "Proxy Password", "copyurladdress": "Copy URL Address", "copyworkingcopyroute": "Copying workingcopy path", "warehouseservices": "Warehouse Services", "associatedsystem": "Associated system", "associatedsubsystems": "Associated subsystems", "branchpath": "Branch Path", "tagpath": "Tag Path", "warehousesynchronization": "Warehouse synchronization", "branchtobesynchronized": "Branch to be synchronized", "syncexecuteandback": "Warehouse synchronization has been executed in the background", "typename": "Type Name", "repositorylist": "Warehouse List", "vouchertype": "document type ", "pleaserepositoryservice": "Please select a warehouse service first", "branchmerge": "Merge by Branch", "issuemerge": "Merge according to demand", "createmergerequest": "Create MR", "strategytype": "Policy Type", "issuesstatus": "Requirement Status", "fixedsourcebranch": "Fixed source branch", "versionstrategy": "Version strategy", "strategyname": "Policy Name", "accessaddress": "Access address", "apidescrition": "Interface Description", "noapihelpinfo": "There is currently no interface help information available", "speciallytreated": "Specially treated", "syncissues": "Synchronize requirements", "issuessource": "Source of demand", "issuesnumber": "Requirement number", "actionname": "denomination of dive", "triggersystem": "Trigger system", "triggersubsystem": "Trigger subsystem", "triggerversion": "Trigger version", "targetbranchdesc": "Separate multiple branches with English commas and support wildcard matching", "triggerrecord": "Trigger Record", "originalmr": "Original MR", "actiontriggertime": "Action trigger time", "actionexecuteresult": "Action execution results", "helpdesc": "Help description: Click on the variable name to copy the variable. The variable is replaced with the actual value when the action is triggered, and supports the syntax of the Freemarker template engine.", "selectoriginbranchandtargetbranch": "Please select the source and target branches", "unknownrequirement": "Unknown requirement", "effectivedemand": "Effective demand", "invaliddemand": "Invalid demand", "mergerequestdesc": "MR Description", "issuesvalid": "Requirement effectiveness", "retrievesubmissionlogs": "Retrieve submission logs", "addmergeissues": "Add demand to be merged", "inputissuesnumberdesc": "Manually input requirement numbers, separated by commas", "issueslogmaxcount": "The number of retrieved submission log entries cannot be less than 1", "pleaseselectanotificationtemplate": "Please select a notification template", "pleaseselectthenotificationobject": "Please select the notification object", "pleaseselecttemplateandreceiver": "Please select a template and recipient", "pleaseselectanaction": "Please select an action", "exceptionnotification": "Exception Notification", "pleaseenterthetriggerpointname": "Please enter the trigger point name", "policylist": "Policy List", "confirmclearconfiguration": "Confirm Clear Configuration", "confirmdeleteconfig": "Confirm to delete the configuration: Action", "pleaseselectaproject": "Please select a project", "temporarilyunavailablecreatemr": "Unable to create MR temporarily", "pleaseselectatleastonerequirement": "Please select at least one requirement", "mergertype": "MR type", "versionnobuildnewmerger": "This version cannot create a new MR", "chooseastrategtfromtable": "Please select a strategy from the table", "actionsubject": "Operation object", "triggerlog": "View trigger records", "mergenumber": "MR number", "allrequirements": "All requirements", "conflictingneeds": "Conflicting needs", "mergedrequirements": "Merged requirements", "triggerappmodule": "Trigger module", "codereview": "code review", "copycurrenturl": "Copy current URL", "thereiscurrentlynoerrormessage": "There is currently no error message", "copycurrentid": "Copy current ID", "linebylinecomparison": "Line by line comparison", "leftandrightcomparison": "Left and right comparison", "postcomments": "Post comments", "encounteringconflictingneeds": "Encountering conflicting needs", "mergerequeststatus": "Merge <PERSON>", "consolidationresult": "Merge Results", "postmergerequestcomments": "Posted MR comments", "changestofiletarget": "Changes to file {target}", "commentedonaline": "Commented on a line", "addconfig": "Edit Configuration", "editconfig": "Add Configuration", "branchname": "Branch name", "editbranch": "Edit Branch", "addbranch": "Add Branch", "confirmactiondatamessage": "This action will cause data loss. To prevent unexpected operations, additional actions are required to confirm your actual intention. Please enter the branch name <span class='text-danger'>{target}</span> to continue the operation or close this dialog box to cancel the operation.", "confirmdeletebranch": "Confirm Delete Branch", "branchnotsame": "Branch name mismatch", "confirmdeletetag": "Are you sure to delete the label", "tagorbranch": "Label or Branch", "finalcommit": "Final submission:", "backparent": "Return to upper level", "nocommitfile": "This warehouse has not yet executed code submission, and there are currently no files available", "edittag": "Edit Label", "addtag": "Add tags", "tagname": "Tag name", "nogroupmembers": "There are currently no group members available", "userauth": "User Authorization", "groupauth": "Group Authorization", "delauth": "Are you sure to delete this permission", "expiresat": "Deadline", "usergourpname": "User Name/Group Name", "readwrite": "Reading and writing", "notauth": "No permissions", "exporting": "Exporting", "editauth": "Edit Authorization", "group": "group", "protectbranch": "Protect Branch", "delprotectbranch": "Are you sure to delete the protected branch", "addprotectbranch": "Add Branch Protection", "selectresource": "Selected resources", "commitcountinfo": "Currently loaded {allcount} items, found submission {currentcount} items that meet the criteria", "choosebranchortag": "Please select a branch or label", "commituserorlogkeyword": "Submitter or submission log keyword", "packupcontent": "Collapse the comparison content", "continuesearch": "Continue to search", "loadmore": "Load more", "issuedetail": "Requirement Details", "copyfileurl": "Copy File Path", "copyfilename": "Copy File Name", "clicktoexpandmore": "Click to expand more", "thisdifferencehasbeenfolded": "This difference has been folded", "clicktoexpandthedetails": "Click to expand the details", "binaryfilecannotviewcontent": "This file is a binary file and currently does not support viewing specific content online,", "clickheretodownloadthefile": "Click here to download the file", "nochangesmade": "No changes made", "thefileisempty": "The file is empty", "filedeleted": "file has been deleted", "defaultbranch": "Default Branch", "mainbranch": "main branch ", "rowadd": "Add Rows", "rowdelete": "Line deletion", "filechangenumber": "File changes", "handlemergerequest": "Process MR", "revokerequirement": "Revoke requirement", "backmergerequest": "Return to MR", "branchproject": "Branch protection", "filenameadjusttotarget": "Change the file name from {fromfileName} to {tofileName}", "binaryfilenosupportview": "This file is binary and currently does not support viewing"}, "documentonline": {"currentpagesearch": "Help related to the current page", "openhelp": "Open Help Center", "openhelpdocument": "Open Help on a New Page", "searchhelp": "Search Help", "helpcenter": "Help Center", "whathelp": "May I help you?", "problemdes": "Problem Description", "selectoneresource": "Please select at least one resource", "noaddauthresource": "There are currently no resources to add permissions to", "relcurrclass": "Associate with current classification", "unlassifieddoc": "Unclassified documents", "representmenupage": "Represent menu page", "representsubpages": "Represent Subpages"}, "dr": {"assetconfig": "Asset allocation", "scenarioconfig": "Scenario configuration", "centername": "Center Name", "basicservices": "Basic services", "associationmodel": "Association model", "network": "network", "migrationdirection": "Migration direction", "datareferencetip": "The current data is referenced by the scene and cannot be deleted", "organizationalstructure": "organizational structure", "affiliatedorganization": "Affiliated organization", "currentoranghaschildnodelete": "The current organizational structure has child levels that cannot be deleted", "currentorangbindusernodelete": "The current organizational structure is bound to a user and cannot be deleted", "selectatleastmodel": "Select at least one model", "applicationtypename": "Application Type", "org": "Recovery institutions", "fileconfigpath": "Configuration file path", "datacenterrel": "Associate Data Centers", "selectdatacenter": "Select at least two data centers", "publicapplication": "public service", "combopname": "Automated orchestration", "dependency": "rely on", "dependencyservice": "dependent service", "scencedependency": "<PERSON><PERSON><PERSON>penden<PERSON>", "servicedependency": "Service Dependency", "scenarioplan": "Scenario Plan", "relyonmyservices": "Rely on my services", "servicerelyon": "The service I rely on", "dependentapp": "Dependent applications", "preparation": "reserve plan", "exerciseplan": "Exercise plan", "dependencyscene": "Dependent scenarios", "sourcedatacenter": "Source Data Center", "targetdatacenter": "Target Data Center"}, "diagram": {"widgetlist": "Element List", "widget": "Element", "lane": "Swimming lanes", "widgetmanage": "Element Management", "dragtosort": "Drag to change sorting", "pleaseselectwidget": "Please select at least one element", "commoncatalog": "Normal directory", "cmdbcatalog": "Configuration Item Catalog", "catalogitem": "Catalog Entry", "childitem": "Child nodes", "relpath": "Relationship Path", "graph": "Architecture diagram", "catalogtemplate": "The templates that can be used in the current directory", "ispublished": "Published", "tofront": "Move to the top", "toend": "Move to the bottom", "publishversion": "Release version", "confirmcheckversion": "Are you sure to submit the current version for review?", "confirmandpublish": "Pass and publish", "line": "straight line", "orth": "orthogonal", "startci": "Starting model", "endci": "Endpoint model", "autofill": "Auto Fill", "beginci": "Starting point model", "snapshotedit": "Generate Snapshot&Edit", "startciseted": "Starting point model has been set", "allowlinkout": "Allow connection", "allowlinkin": "Allow connection", "allowresize": "Allow size changes", "allowdelete": "Allow deletion", "allowmove": "Allow movement", "nocientityname": "No display name", "allowselect": "Allow selection", "releaselock": "Unlock", "privatecatalog": "Personal directory", "publiccatalog": "Public directory", "releaseconfirm": "Are you sure to publish the current architecture diagram?", "edgemode": "line mode", "sourcemarker": "Starting arrow", "targetmarker": "End arrow", "classic": "classical", "block": "block", "cross": "overlapping", "larger": "enlarge", "smaller": "narrow", "importtemplate": "Import Template", "strictmode": "Strict box selection mode", "changestatusconfirm": "Are you sure to change current version to {target}?", "editingversion": "Editing version", "createuser": "Creator", "cientityinfo": "Configuration Item Information", "changedata": "Change data", "diagramcatalog": "Architecture diagram directory", "changearchitecture": "Architecture changes", "changelist": "Change List", "isactive": "Whether to publish", "nodiagramchange": "There are currently no architecture changes", "iscurrentversion": "Is it the current version", "exportsvg": "Export SVG", "exportpng": "Export PNG", "nootherversion": "There are currently no other versions available", "otherversion": "Other versions", "deleteversionconfirm": "Are you sure to delete version: {target}?", "metro": "Subway avoidance", "manhattan": "Orthogonal avoidance", "dynamicwidget": "Dynamic Elements", "startcientity": "Starting point configuration item", "onlyhasdiagram": "Just looking at the pictures", "nextstatus": "Flow status", "editing": "Editing in progress", "activing": "Activing", "source": "Source restrictions", "waitfordo": "Pending processing", "torel": "Downstream relationship", "fromrel": "Upstream relationship", "rel": "Related relationships", "reltypeconfig": "Connection type configuration", "edgetype": "line segment"}, "license": {"isbanmodule": "Disable module upon expiration", "graceperiod": "Delay grace days"}, "alert": {"alerttype": "Alarm Type ", "alert": "give an alarm", "isnormalattr": "As a regular attribute display", "closesuccess": "Close successfully", "istop": "topped", "onlysearchparentalert": "Only search for parent alarms", "searchallalert": "Search for all alarms", "closeselectedalert": "Close selected alarms", "openselectedalert": "Open the selected alarm", "deletematchalert": "Delete matching alarm", "allalert": "All Alarms", "alerttopo": "Alarm topology", "alertdetail": "Alarm details", "applychildalert": "Simultaneously apply sub alarms", "transferworker": "Transfer to the handler", "transferteam": "Transfer to processing team", "reportdata": "reported data", "eventaudit": "Event record", "alertnotexists": "Alarm does not exist or has been deleted", "childalert": "Sub alarm", "dealresult": "Processing results", "createalert": "Create an alarm", "defaultstatus": "Defaults on", "joinalert": "Merge into alarms", "uniquekey": "unique", "alertsign": "Alarm features"}, "runnergroup": {"ruletips": "The request for login authentication needs to carry a header as a rule expression (note that the header parameter in the expression is all lowercase). If the value of the expression after execution is true, the executor group will take effect, and false and syntax exceptions will not take effect. For example: ${env}==\\ bit \\&&(${test}==\\ 1 \\ | | ${test2}==\\ aaa \\)"}}