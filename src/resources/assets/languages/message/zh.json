{"deletesuccess": "删除成功", "deletefailed": "删除失败", "deletecancel": "已取消删除", "updatesuccess": "修改成功", "addsuccess": "添加成功", "savesuccess": "保存成功", "savefailed": "保存失败", "executesuccess": "操作成功", "cancelsuccess": "取消成功", "collectionsuccess": "收藏成功", "importsuccess": "导入成功", "importfailed": "导入失败", "recoversuccess": "恢复成功", "commitsuccess": "提交成功", "refreshsuccess": "刷新成功", "copysuccess": "复制成功", "pastesuccess": "粘贴成功", "uploadsuccess": "上传成功", "uploadfailed": "上传失败", "validatesuccess": "校验成功", "targetisexists": "{target}已存在", "sessionexpired": "会话已超时，请重新登录", "urlnotfound": "接口{target}不存在", "noauth": "暂无访问权限", "nothingchange": "无任何修改", "failed": "{target}获取失败", "cannotrepeat": "{target}不能重复", "nameexists": "名称已存在", "completerequired": "{target}请填写完整", "targetvalidfailed": "{target}验证失败", "pleaseentertruetarget": "请输入正确的{target}", "whatdo": "我们能为您提供什么？", "namelong": "名称过长", "executefailed": "操作失败", "executionresultpop": "执行结果提示", "selectobjdel": "请选择需要删除的对象", "clearsuccess": "清除成功", "clearupsuccess": "清理成功", "cmdb": {"keylevel": "关键层级会显示在最左侧菜单中", "topotype": "开启后此层级才会在拓扑图中显示", "editrelpath": "点击蓝色节点，可设置该节点数据是否显示", "selectci": "请选择关联模型", "topoerror": "当前模型数据无法使用{target}生成拓扑图，请换一种布局。", "notopo": "拓扑图无法显示，请检查当前配置项所属模型类型是否允许展示拓扑。", "diffci": "已选择的配置项不属于同一个模型，请重新选择", "hasnoauth": "您没有权限编辑配置项:{target}", "virtualmodel": "当前模型是虚拟模型，不能添加配置项。", "abstractmodel": "当前模型是抽象模型，不能直接添加配置项。", "autoattr": "激活了自动采集的属性才能在采集映射管理中配置", "cimanage": "拥有此权限则自动拥有关于当前模型的所有权限", "newcientity": "拥有此权限可以新增配置项，如果没有事务管理权限则不能提交生效", "editcientity": "拥有此权限可以编辑配置项，如果没有事务管理权限则不能提交生效", "deletecientity": "拥有此权限可以删除配置项，如果没有事务管理权限则不能提交生效", "recovercientity": "拥有此权限可以恢复已删除的配置项", "viewcientity": "拥有此权限可以查询配置项", "transactionmanage": "拥有此权限可以提交未生效的事务或删除未提交的事务，并且自动拥有新增，修改和删除配置项的权限", "viewpassword": "拥有此权限可以查看当前模型下所有配置项的密码字段", "noedit": "保存后不能修改", "abstractmodelextend": "抽象模型可以被继承，但不能添加配置项", "keymodel": "关键模型会显示在最左侧菜单中", "activedate": "单位是天，有效日期为零代表永不过期", "xmlconfig": "配置文件为XML文件，通过SQL语句抽取系统内部任意数据表的数据生成配置项模型，虚拟模型中的数据不能修改和删除，但可以被其他模型关联或直接查询", "cientitynotexists": "没有找到符合条件的配置项", "importhelp1": "点击“下载模板”标签页的左边树形菜单下载导入模板。", "importhelp2": "文件上传后可以多次导入，导入前请删掉不需要导入的文件。", "importhelp3": "请不要编辑模板标题行，如果配置项类型发生改变，请重新获取下载模板。", "importhelp4": "多个属性之间用英文逗号分隔，关系型属性请按照目标配置项类型的显示名表达式输入，如果属性值能匹配到多个配置项，系统会自动关联多个目标配置项。", "importhelp5": "全局更新：配置项的所有属性都以表格内容为准，全部更新；非全局更新只更新表格中存在的属性，不存在的属性保持不变。", "importhelp6": "增量导入：只导入id列为空的配置项。", "importhelp7": "存量导入：只导入id列不为空的配置项，如果id对应的配置项不存在，则忽略。", "importhelp8": "全量导入：添加id列为空的配置项，编辑id不为空的配置项，如果id对应的配置项不存在，则忽略。", "importhelp9": "附件类型不支持导入。", "importhelp10": "密码类型如果值等于********代表不修改。", "importhelp11": "用户类型需填写用户ID。", "importhelp12": "分组和角色类型填写的是名称，多个值以英文逗号分隔。", "importaddconfirm": "增量导入只会导入id列为空的配置项。<br/>是否确认增量导入数据？", "importupdateconfirm": "存量导入只会导入id列不为空的配置项，如果id对应的配置项不存在，则忽略。<br/>是否确认存量导入数据？", "importaddupdateconfirm": "全量导入会导入所有数据，如果id对应的配置项不存在，则忽略。<br/>是否确认全量导入数据？", "downloadimporttemplate": "请点击左边树形菜单下载对应模型的导入模板", "exceptiontemplate": "自定义异常信息，范例：属性“{label}”的值“{value}”需要符合IP格式", "customviewconfighelp": "配置是一段javascript代码，需要定义包含methods等属性的对象", "customviewcontenthelp": "模板需要符合vue模板语法规范，必须指定一个根标签，一般是div", "customviewcontenthelp1": "数据集，所有数据都从dataList中获取，dataList是一个json对象数组", "customviewcontenthelp2": "当前页，系统默认返回第一页数据，可以通过$emit(changeCurrentPage,数字)来修改", "customviewcontenthelp3": "每页大小，默认20，可以通过$emit(changePageSize,数字)来修改", "customviewcontenthelp4": "是否有下一页数据，系统会自动计算，不能修改"}, "process": {"required": "【{target}】不能为空", "complete": "【{target}】请填写完整", "success": "【{target}】验证成功", "prioritymsg": "【{target}表单联动导致优先级为空", "cannotnodelist": "节点列表数据获取失败！", "notsaveflow": "该流程还未保存！", "notinitdata": "初始化获取数据失败！", "timerhasonelink": "定时节点只能关联一个后续节点", "nodeorphaned": "节点被孤立", "nodenofrontnode": "节点没有前置节点", "nodenopostnode": "节点没有后置节点", "nodemusthaveoutgoingline": "节点必须有连出线", "nodemusthaveinputgoingline": "节点必须有连进线", "nodecannotbeconnectedstartnode": "当前节点不能和开始节点相连", "startnodeonlyoneline": "开始节点只能关联一个后续节点", "changecrenotendnode": "变更创建节点不允许直接连结束节点", "pleasestartscoringsetting": "请启动评分设置", "recreatenewjobtip": "流程回退后，再次流转至当前步骤时，重新创建自动化作业并执行", "batchcreatejobpolicyvalid": "批量创建作业，表格组件至少要被自动化作业参数引用一次", "batchjobpolicytip": "循环遍历列表数据，创建多个作业", "jobpolicycolumn": "循环遍历列表数据，合并为数组", "eoahasonelink": "审批节点只能有一根线连出！", "eoanodenobacklink": "审批节点只能回退到一个节点", "eoalinkouttip": "审批节点至多只有两条线连出", "nodenobacklink": "不能连回退线"}, "autoexec": {"savedeadlinenolimit": "不设置代表保存期限不限制", "gotoactivate": "前往激活", "successimporttip": "，导入仅生成待审核版本，需要审核通过后才可激活使用。", "replaceimporttip": "以下工具存在待审核版本，是否替换：", "emptyfiltervalue": "不在过滤器范围内", "notfoundsys": "在系统中未找到对应资产", "executerunnertip": "组合工具仅包含runner或者sql执行方式，无需设置执行目标", "notsetexecutertip": "组合工具中，每个执行阶段均已预设执行目标，此处无需设置执行目标", "norunnerphaserunnergrouptips": "组合工具不包含runner或者sql执行方式的阶段，无需设置执行器组"}, "knowledge": {"notcatalog": "无文本目录", "circleauth": "您不属于任何知识圈，请联系管理员处理"}, "report": {"addreportwidget": "请添加报表组件", "definetemplate": "请定义模板内容", "widgetcreatefail": "组件创建失败，请打开开发者模式查看异常详情。"}, "framework": {"atleastonestaticsource": "请至少添加一个静态数据源下拉选项", "plecompletecondition": "请填写完整过滤条件", "teamcounttarget": "共计选中{target}个分组", "sqlactiondesc": "增加监控：开始监控指定sql，删除监控：解除监控指定sql，清空所有：清空所有sql监控", "sqliddesc": "sql id是mybatis sql语句mapper文件中的语句id，请联系研发获取，如果添加*，代表监控所有SQL语句，系统最多只会保存最新的1000条审计记录。", "stopusetip": "停用后，公告将从消息列表消失", "privatematrixtip": "当前矩阵为私有数据源，无法删除", "delmatrixtip": "当前矩阵被引用，无法删除", "xmldesc": "配置文件为XML文件，通过SQL语句抽取系统内部任意数据表的数据生成配置项模型，虚拟模型中的数据不能修改和删除，但可以被其他模型关联或直接查询。", "viewmatrixsql": "视图字段定义，需要SQL语句返回对应列", "reqid": "必须包含id字段", "requuid": "必须包含uuid字段", "reqattrs": "下列字段需要在上面attrs中定义才生效", "plesrow": "请先添加字段数据", "cimatrixsql": "模型属性定义，需要SQL语句返回对应列", "reqciid": "必须包含id字段，作为配置项主键", "reqciname": "必须包含name字段，作为配置项名称", "reqciattrs": "属性列需要在上面attrs中定义才生效", "notcaneditmatrixtip": "当前矩阵已被表单引用，无法修改配置", "keycanotdel": "该字段下有数据，不允许删除", "targetattrscanotdel": "{target}属性已被表单引用，无法删除", "dataspftdesc": "数据规范设定了输入和输出参数，如果接口返回的数据不符合数据规范要求，需要根据规范对数据进项转换，否则会影响正常使用。", "tokentip": "BearerToken方式支持OAuth等认证方式，请把从认证服务器获得的token填入下面输入框。", "apitip": "内部验证主要应用在转换内部接口，系统会自动传送当前用户认证信息。如获取不到用户登录信息，则访问失败。", "noauthtip": "当前请求无需任何认证", "jshelp": "编写javascript，将原请求参数格式转换成目标地址所需的数据格式，原请求参数不符合json格式或不定义转换规则，则直接传送原请求参数。仅支持ES5语法。", "syncmodedesc": "追加模式：直接写入新数据，主键相同的数据会被替换；替换模式：先清空数据再写入新数据。", "effectivetimedesc": "不设或<=0代表永远有效，有效天数到达后数据会自动删除。注意：重新设置有效天数对已经存在的数据无效。", "notdelformtip": "当前表单被流程引用，无法删除", "isdurabledesc": "持久订阅：本系统异常期间，消息队列会保存当前订阅主题收到的消息，待本系统恢复正常后重新推送。临时订阅：本系统异常期间，消息队列不会保存消息，这段期间收到的消息将会被丢弃。", "tagentconfirm": "是否确认重启Tagent？", "tagentresetpwd": "是否确认重置Tagent密码？", "addtagentpla": "匹配分组、网段IP、代理名称或代理地址", "installpackagetip": "升级包用于agent升级，升级时，将根据目标主机的OS类型、CPU架构，找到匹配的安装包。下列选项中的【CPU架构】可选择‘default’，被标识为‘default’的安装包，可适配所有的CPU架构。注意：升级包仅包含源码，且只能压缩成tar", "isreplacepkg": "安装包已存在，是否替换", "linuxpkg": "Linux｜Unix安装包：tagent_linux.tar", "windowspkg": "Windows安装包：tagent_windows_x32.tar、tagent_windows_x64.tar（windows安装包内嵌了Perl运行时和7z工具）", "autoinstallwindows": "获取子目录bin下的install.sh或者install.vbs(Windows)", "autoinstalltagent": "自动安装需要在某个可以http或ftp下载的地方放置tagent的安装包，下面的安装样例脚本中的地址和租户名称需要根据实际情况进行修改<br/>变量：RUNNER_ADDR是执行节点的URL，根据网络是否能够连通来选择，只要网络能通，选择任意一个RUNNER效果是相同的。<br/>tenant租户选择，根据系统安装设置的租户来进行输入。", "autolinuxscripts": "Linux安装，以root用户运行", "autounixscripts": "Linux安装，以app用户运行，监听2020端口", "manualdectip": "上传安装包到服务器，解压到/opt/tagent", "manualdecwindowstip": "上传安装包到服务器，解压到c:/tagent", "upgradedesc": "根据IP:PORT选择和根据网段选择必须选择其中一个", "checktagentnumberdesc": "代理组、根据IP:PORT选择、根据网段选择、必须选择其中一个", "apihelperror": "获取接口帮助信息失败", "notapihelp": "当前接口{target}无帮助信息", "usercounttarget": "共计选中{target}个用户", "selectrolecount": "已选中{target}个角色", "worktimetip": "最终的选定的日期的结果与用户编辑时间段有关，那一天必须先有工作时间段排班日期才生效", "activedversiontip": "版本已经被修改，需保存之后再激活", "formimpottip": "当前表单的版本校验不通过，是否确认导入时放弃未保存的修改？", "exportformtip": "当前表单的版本校验不通过，是否确认导出时放弃未保存的修改？", "templateparamstip": "左侧“标题”或“内容”输入框获取光标后，点击下方参数，自动填充对应代码段", "timeouttip": "设置一个请求的有效时间，单位：秒；<br>0代表无限制；<br>如果请求时效大于0，request header需要包含键值对：<br>x-access-date：时间戳", "qpstip": "接口每秒允许访问的次数，单位：次/秒；<br>0代表不限制", "calldetailerror": "获取调用详情失败", "maxnumbervalid": "最大值不得小于等于最小值", "matrixtip": "勾选PC端/移动端需要展示或搜索的属性，搜索和显示列的显示互不影响", "matirxhttpurl": "请选矩阵中保存http或https链接的属性", "staticbatchdatatip": "1、每一行代表一个选项，且每一行的值不可以重复。<br>2、值和名称用,分隔，可以只有值，名称会自动补充。<br>3、使用符号-作为前缀代表层级关系，有多少个-代表多少级，最多不超过5级。<br>4、数组长度不可大于500。", "pastecelltip": "当前单元格不为空，确认使用复制内容替换当前内容？", "replacenewcomp": "当前单元格不为空，是否替换成新组件？", "clearcelltip": "合并单元格会清空以下组件", "cusscenecomptip": "自定义场景仅支持从[主场景]的已有组件中，选择组件", "defultscenetip": "自定义表单场景，可基于主场景组件，修改表单布局，隐藏、禁用组件或修改组件数据源。表单场景可关联流程节点，实现同一流程，不同节点的处理页面展示不同的表单布局。工单的查看页面统一展示表单主场景。", "notdelscenetip": "当前表单场景已被引用，无法删除", "forminputdatatip": "帮助：输入数据测试表单对数据的处理是否正常，输入数据支持两种格式：1、直接使用输出数据。2、简化格式：{ComponentUuid1:value1,ComponentUuid2:value2}", "formcostomhelp": "模板需要符合vue模板语法规范，必须指定一个根标签，一般是div，支持所有iView组件，也支持以下内部组件", "formcustomjshelp": "配置是一段javascript代码，需要定义包含methods等属性的对象，仅支持ES5语法。例如", "selectcusconfig": "选择当前自定义组件可以使用的基本配置", "selectcusaction": "选择当前自定义组件可以使用的交互操作", "leastoneselectattr": "请至少选择一个矩阵固有属性", "staticrepeat": "静态数据选项存在重复数据，请重新编辑", "syncsceneattrtip": "默认场景包含属性A、B、C，且当前场景不包含A、B、C，将A、B、C属性添加至当前场景", "editwechattouser": "要与企业微信后台的用户管理中用户账号一致", "regex": "正则校验", "regextip": "自定义正则表达式，校验文本框输入合法性；未填写时不校验", "regularexpression": "正则表达式", "regexvalidtip": "输入校验失败后，用户可根据此校验提示重新输入", "regexvalidplaceholder": "正则表达式具体含义，如：仅包含数字", "validtip": "校验提示", "datasourceselectmessage": "引用的表格输入组件已被删除，请重新配置属性数据源"}, "dashboard": {"templatehelp": "模板需要符合vue模板语法规范，必须指定一个根标签，一般是div，支持所有iView组件。数据源结果以数组的形式返回，根属性为：dataList，访问数据时请直接迭代dataList获取需要的数据。", "confighelp": "配置是一段javascript代码，需要定义包含methods、computed等属性的对象。", "imagehelp": "只支持上传图片，引用图片路径范例：&lt;img&nbsp;src=&quot;&#123;file:filename.png&#125;&quot;&sol;&gt;"}, "apierror": "接口请求错误", "allimportedsuccessfullytotaltarget": "全部导入成功，共{target}项", "successfullyimportitemstarget": "导入成功{target}项", "failedtoimportitemstarget": "导入失败{target}项", "onlyasinglefilecanbeimported": "仅支持导入单个文件", "thefilesuffixmustbe": "文件后缀必须是", "singlefilescanbeuploaded": "支持上传单个文件", "multiplefilescanbeuploaded": "支持上传多个文件", "supportuploadingmultiplefileswithsuffixtarget": "支持上传多个后缀为{target}的文件", "supportuploadingsinglefileswithsuffixtarget": "支持上传单个后缀为{target}的文件", "mustbeanarray": "必须是个数组", "virtualrootnode": "虚拟的根节点", "flattentreetip": "flattenTree的第二个参数必须是个有返回值的function", "providecorresponding": "请提供相应的{target}", "issthplugin": "这是{target}插件", "targetnoexist": "{target}不存在", "invalidjson": "无效json数据", "nodenoexist": "不存在该{target}节点", "plugin": {"lineoutreject": "该节点表示拒绝线连出！", "lineinreject": "该节点表示拒绝线连入！", "cannotlinein": "不能有线连入", "cannotlineout": "不能有线连出", "nolinein": "没有线连入", "nolineout": "没有线连出", "notalonebystart": "不能被起始节点孤立", "notalonebyend": "不能被结束节点孤立", "thisnode": "该节点", "unknowfiletype": "无法确定上传文件的类别。", "cannotuploadfile": "无法上传的文件：", "cannotinsertpic": "无法在当前位置插入图片", "cannotresizeimgurl": "无法获取重设大小的图片URL", "inputimgtitle": "输入图片标题", "insertimgfail": "插入图片失败", "italic": "倾斜", "imgsizeresetfail": "选择重设大小的图片失败", "emptyurl": "URL不可以为空。", "linkhasnourl": "此链接没有设置网址", "mediaurlnotsupport": "不支持此媒体URL。", "urlpastetips": "提示：将URL粘贴到内容中可更快地嵌入", "togglecaptionoff": "关闭表标题", "togglecaptionon": "打开表标题", "correcttimerangedesc": "请输正确的时间段,格式为10:00-12:00,多个用空格隔开.", "serialnumberdesc": "请输入序号,格式范例：1或1.A或1.A.1", "variablenamedesc": "变量名只能以字母、数字、下划线和.组成，且开头不能是数字", "donotinput": "请不要输入&lt; &gt; &quot; &apos; 等特殊符号", "supporttype1": "仅支持汉字、字母、数字和特殊字符(._-)", "supporttype2": "仅支持字母、数字和特殊字符(._-)", "supporttype3": "仅支持字母、数字、斜杠和下划线", "highriskcode": "存在高危代码", "enName": "仅支持字母、数字、下划线"}, "incorrectformat": "格式不正确", "accordingformat": "请按照格式要求输入", "modifyfail": "修改失败", "reportsuccess": "上报成功", "notsavedataupdateredirecttip": "您有未保存数据，是否保存当前修改并跳转新页面？", "passcode": "请输入长度在8~20之间的字符串，至少有字母、数字、特殊字符其中2种组合", "editaftersave": "请先保存之后再编辑", "commentsucces": "评论成功", "syncsuccess": "同步成功", "downloadsuccessful": "下载成功", "diagram": {"versionchecking": "当前版本正在审核中，无法编辑", "deletetemplateconfirm": "当前模板是自动填充模板，删除后对应的目录或配置项不再自动生成相关架构图，是否确认删除？", "deletetemplateanddiagramconfirm": "当前模板是自动填充模板，并且存在修改记录，删除后对应的目录或配置项不再自动生成相关架构图，修改记录也会一起删除，是否确认删除？", "deletecatalogconfirm": "目录删除后目录下的所有架构图以及修改记录也会一起删除，是否确认？", "lockmessage": "当前架构图已被 {user} 锁定编辑，锁定时间 {time}", "lockdiagramconfirm": "是否确认锁定当前架构图并进入编辑模式？", "editingversionnotfound": "当前架构图不存在编辑中的版本", "noauthtoedit": "您没有权限编辑当前架构图", "isactivenotfound": "当前架构图不存在已激活版本", "editingversionirregular": "当前版本处于非编辑状态，不能进行编辑"}, "runsuccess": "执行成功"}