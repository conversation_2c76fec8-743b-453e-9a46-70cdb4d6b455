{"deletesuccess": "Delete succeeded", "deletefailed": "Delete failed", "deletecancel": "Delete canceled", "updatesuccess": "Modification successful", "addsuccess": "Added successfully", "savesuccess": "Saved successfully", "savefailed": "Save failed", "executesuccess": "Operation succeeded", "cancelsuccess": "Cancel successful", "collectionsuccess": "Collection success", "importsuccess": "Imported successfully", "importfailed": "Import failed", "recoversuccess": "Recovery succeeded", "commitsuccess": "Successful commit", "refreshsuccess": "Refresh successfully", "copysuccess": "Copy successful", "pastesuccess": "<PERSON><PERSON> successfully", "uploadsuccess": "Upload successful", "uploadfailed": "Upload failed", "validatesuccess": "Validation succeeded", "targetisexists": "{target} already exists", "sessionexpired": "The session has expired, please log in again", "urlnotfound": "Interface {target} does not exist", "noauth": "No access right now", "nothingchange": "Nothing change", "failed": "Failed to obtain {target}", "cannotrepeat": "{target} cannot be repeated", "nameexists": "Name already exists", "completerequired": "{target} please complete", "targetvalidfailed": "{target} validation failed", "pleaseentertruetarget": "Please enter the correct {target}", "whatdo": "What can we offer you?", "namelong": "The name is too long", "executefailed": "operation failed", "executionresultpop": "Execution result prompt", "selectobjdel": "Please select the object to delete", "clearsuccess": "<PERSON><PERSON> succeeded", "clearupsuccess": "Cleanup succeeded", "cmdb": {"keylevel": "The key level will be displayed in the leftmost menu", "topotype": "This level will be displayed in the topology map after it is turned on", "editrelpath": "Click the blue node to set whether the data of this node is displayed.", "selectci": "Please select the associated model", "topoerror": "The current model data cannot use {target} to generate a topology map, please change to another layout.", "notopo": "The topology map cannot be displayed, please check whether the model type of the current configuration item allows displaying the topology.", "diffci": "The selected configuration items do not belong to the same model, please select again", "hasnoauth": "You do not have permission to edit the configuration item: {target}", "virtualmodel": "The current model is a virtual model, configuration items cannot be added.", "abstractmodel": "The current model is an abstract model, configuration items cannot be added directly.", "autoattr": "Activate the attribute of automatic collection before it can be configured in the collection map management", "cimanage": "Having this permission automatically owns all permissions on the current model", "newcientity": "With this permission, configuration items can be added. If you do not have transaction management permissions, you cannot submit them to take effect", "editcientity": "With this permission, you can edit the configuration item. If you don't have the transaction management permission, you can't submit it to take effect", "deletecientity": "With this permission, configuration items can be deleted. If you don't have transaction management permission, you can't submit it to take effect", "recovercientity": "Having this permission can restore deleted configuration items", "viewcientity": "Having this permission can query configuration items", "transactionmanage": "With this permission, you can submit or delete uncommitted transactions, and automatically have the permission to add, modify and delete configuration items", "viewpassword": "With this permission, you can view the password fields of all configuration items under the current model", "noedit": "Cannot modify after saving", "abstractmodelextend": "Abstract models can be inherited, but configuration items cannot be added", "keymodel": "The key model will be displayed in the leftmost menu", "activedate": "The unit is days, the effective date is zero means it will never expire", "xmlconfig": "The configuration file is an XML file. The data in any data table in the system is extracted through SQL statements to generate a configuration item model. The data in the virtual model cannot be modified or deleted, but can be associated or directly queried by other models.", "cientitynotexists": "No matching configuration items were found", "importhelp1": "Click the tree menu on the left of the \"Download Template\" tab to download the imported template.", "importhelp2": "After uploading a file, it can be imported multiple times. Before importing, please delete any files that do not need to be imported.", "importhelp3": "Please do not edit the template header line. If the configuration item type changes, please obtain the download template again.", "importhelp4": "Multiple attributes are separated by English commas. For relational attributes, please enter the display name expression of the target configuration item type. If the attribute value matches multiple configuration items, the system will automatically associate multiple target configuration items.", "importhelp5": "Global Update: All attributes of a configuration item are updated based on the table content; Non global updates only update attributes that exist in the table, while non existing attributes remain unchanged.", "importhelp6": "Incremental Import: Only configuration items with an empty id column are imported.", "importhelp7": "Inventory Import: Only configuration items with an id column that is not empty are imported. If the configuration item corresponding to the id does not exist, it is ignored.", "importhelp8": "Full import: Add a configuration item with an empty id column, edit a configuration item with an empty id. If the configuration item corresponding to the id does not exist, it will be ignored.", "importhelp9": "The attachment type does not support import.", "importhelp10": "\"If the password type value is equal to ***************, it means no modification.\".", "importhelp11": "User ID is required for user type.", "importhelp12": "Groups and role types are filled with names, and multiple values are separated by English commas.", "importaddconfirm": "Incremental import only imports configuration items with an empty id column< Br/>Are you sure to incrementally import data?", "importupdateconfirm": "Inventory import only imports configuration items whose id column is not empty. If the configuration item corresponding to the id does not exist, it is ignored< Br/>Are you sure to import stock data?", "importaddupdateconfirm": "Full import will import all data. If the configuration item corresponding to the ID does not exist, it will be ignored< Br/>Are you sure to import data in full?", "downloadimporttemplate": "Please click the tree menu on the left to download the import template for the corresponding model", "exceptiontemplate": "Custom exception information. Example: The value '{value}' of attribute '{label}' needs to conform to the IP format", "customviewconfighelp": "Configuration is a piece of javascript code that requires defining objects that contain attributes such as methods", "customviewcontenthelp": "The template needs to comply with the Vue template syntax specification and must specify a root tag, typically a div", "customviewcontenthelp1": "A dataset, all data is obtained from a dataList, which is an array of json objects", "customviewcontenthelp2": "The system returns the first page of data for the current page by default, which can be modified through $exit (changeCurrentPage, number)", "customviewcontenthelp3": "The size of each page, which is 20 by default, can be modified through $exit (changePageSize, number)", "customviewcontenthelp4": "Whether there is data on the next page will be calculated automatically and cannot be modified"}, "process": {"required": "[{target}] cannot be empty", "complete": "【{target}】Please fill in completely", "success": "【{target}】Verification succeeded", "prioritymsg": "[{target} form linkage results in empty priority", "cannotnodelist": "Failed to obtain node list data!", "notsaveflow": "The flow has not been saved yet!", "notinitdata": "Initialization failed to obtain data!", "timerhasonelink": "The timer node can only have one line connected", "nodeorphaned": "<PERSON><PERSON> is orphaned", "nodenofrontnode": "Node has no front node", "nodenopostnode": "Node has no postnode", "nodemusthaveoutgoingline": "Nodes must have an outgoing line", "nodemusthaveinputgoingline": "Nodes must have inputgoingline", "nodecannotbeconnectedstartnode": "This node cannot be connected to the start node", "startnodeonlyoneline": "The start node can only have one line connected", "changecrenotendnode": "Change creation node is not allowed to directly end", "pleasestartscoringsetting": "Please start scoring setting", "recreatenewjobtip": "After the process is rolled back, when it flows back to the current step, recreate the automated job and execute it", "batchcreatejobpolicyvalid": "Batch creation of jobs, table components must be referenced by automation job parameters at least once", "batchjobpolicytip": "Loop through list data to create multiple jobs", "jobpolicycolumn": "Loop through list data and merge into an array", "eoahasonelink": "Approval nodes can only have one line connected!", "eoanodenobacklink": "Approval nodes can only be rolled back to one node", "eoalinkouttip": "At most, the approval node can only be connected by two lines", "nodenobacklink": "Cannot connect fallback line"}, "autoexec": {"savedeadlinenolimit": "No setting means unlimited storage period", "gotoactivate": "Goto activate", "successimporttip": "The import only generates a pending version, which can only be activated after being approved.", "replaceimporttip": "The following tools have pending versions, whether to replace:", "emptyfiltervalue": "Not within filter range", "notfoundsys": "The corresponding asset was not found in the system", "executerunnertip": "The combination tool only includes the runner or SQL execution method, and there is no need to set the execution target", "notsetexecutertip": "In the combination tool, each execution stage has preset execution goals, so there is no need to set execution goals here", "norunnerphaserunnergrouptips": "The combination tool does not include a runner or SQL execution method, and there is no need to set an executor group"}, "knowledge": {"notcatalog": "No text catalog", "circleauth": "You do not belong to any knowledge circle, please contact the administrator for handling"}, "report": {"addreportwidget": "Please add a report widget", "definetemplate": "Please define the template content", "widgetcreatefail": "Component creation failed. Please open developer mode to view exception details."}, "framework": {"atleastonestaticsource": "Please add at least one static data source drop-down option", "plecompletecondition": "Please complete the filter condition", "teamcounttarget": "A total of {target} teams are selected", "sqlactiondesc": "Add monitoring: Start monitoring specified sql, delete monitoring: Release control of specified sql, clear all: Clear all sql monitoring", "sqliddesc": "The sqll id is the statement id in the mybatis sql statement mapper file. Please contact the R&D department to obtain it. If you add *, it means that all SQL statements are monitored. The system will only save the latest 1000 audit records.", "stopusetip": "After deactivation, the announcement will disappear from the message list", "privatematrixtip": "The current matrix is a private data source and cannot be deleted", "delmatrixtip": "The current matrix is referenced and cannot be deleted", "xmldesc": "The configuration file is an XML file that extracts data from any data table within the system through SQL statements to generate a configuration item model. The data in the virtual model cannot be modified or deleted, but can be associated or directly queried by other models.", "viewmatrixsql": "View field definitions require SQL statements to return corresponding columns", "reqid": "Must contain an id field", "requuid": "Must contain a uuid field", "reqattrs": "The following fields need to be defined in the above attrs to take effect", "plesrow": "Please add field data first", "cimatrixsql": "Model attribute definitions require SQL statements to return corresponding columns", "reqciid": "The ID field must be included as the configuration item primary key", "reqciname": "The name field must be included as the configuration item name", "reqciattrs": "The attribute column needs to be defined in the above attrs to take effect", "notcaneditmatrixtip": "The current matrix has been referenced by a form, and the configuration cannot be modified", "keycanotdel": "There is data under this field and deletion is not allowed", "targetattrscanotdel": "The {target} attribute has been referenced by the form and cannot be deleted", "dataspftdesc": "The data specification sets input and output parameters. If the data returned by the interface does not meet the requirements of the data specification, it is necessary to convert the data according to the specification, otherwise normal use will be affected.", "tokentip": "The BearerToken method supports authentication methods such as OAuth. Please fill the token obtained from the authentication server into the input box below.", "apitip": "Internal authentication is mainly used to convert internal interfaces, and the system will automatically transmit the current user authentication information. If the user login information cannot be obtained, the access fails.", "noauthtip": "The current request does not require any authentication", "jshelp": "Write javascript to convert the original request parameter format into the data format required for the target address. If the original request parameter does not conform to the json format or does not define conversion rules, the original request parameter will be directly transmitted.Only supports ES5 syntax.", "syncmodedesc": "Append mode: Directly write new data, and data with the same primary key will be replaced; Replacement mode: Clear data before writing new data.", "effectivetimedesc": "If it is not set or<=0, it means it is always valid, and the data will be automatically deleted when the valid number of days reaches. Note: Resetting the valid days is invalid for existing data.", "notdelformtip": "The current form is referenced by the process and cannot be deleted", "isdurabledesc": "Persistent subscription: During system exceptions, Message Queuing will save the messages received from the current subscription topic and push them again after the system returns to normal. Temporary subscription: During system exceptions, Message Queuing will not save messages, and messages received during this period will be discarded.", "tagentconfirm": "Are you sure to restart Tagent?", "tagentresetpwd": "Are you sure you want to reset the Tagent password?", "addtagentpla": "Match packet, segment IP, proxy name, or proxy address", "installpackagetip": "The upgrade package is used for agent upgrades. During the upgrade, the matching installation package will be identified based on the target host's OS type and CPU architecture‌. In the options below, ‌[CPU architecture]‌ can be set to 'default'. Packages marked as 'default' are compatible with all CPU architectures‌.‌Note:‌ The upgrade package contains only source code and must be compressed into a tar archive‌", "isreplacepkg": "The installation package already exists, do you want to replace it", "linuxpkg": "Linux | Unix installation package: agent_ linux.tar", "windowspkg": "Windows installation package: agent_ windows_ x32.tar、tagent_ windows_ X64.tar (the Windows installation package contains the Perl runtime and 7z tools)", "autoinstallwindows": "Get install.sh or install.vbs under the subdirectory bin (Windows)", "autoinstalltagent": "Automatic installation requires placing the installation package of the agent in a place that can be downloaded via http or ftp. The address and tenant name in the following installation sample script need to be modified according to the actual situation  <br/>Variable: RUNNER_ ADDR is the URL of the execution node, which is selected based on whether the network can be connected. As long as the network can be connected, selecting any one of the RUNNER options has the same effect. <br/>Tenant selection is based on the tenant set by the system installation.", "autolinuxscripts": "Linux installation, running as root", "autounixscripts": "Linux installation, running as an app user, listening to port 2020", "manualdectip": "Upload the installation package to the server and unzip it to/opt/tmagent", "manualdecwindowstip": "Upload the installation package to the server and unzip it to c:/tmagent", "upgradedesc": "One of the following must be selected based on IP: PORT selection and network segment selection", "checktagentnumberdesc": "Agent group, selected based on IP: PORT, selected based on network segment, one of which must be selected", "apihelperror": "Failed to obtain interface help information", "notapihelp": "The current interface {target} has no help information", "usercounttarget": "{target} users selected in total", "selectrolecount": "{target} roles selected", "worktimetip": "The final result of the selected date is related to the user's editing time period, and that day must first have a working time period scheduling date to take effect", "activedversiontip": "The version has been modified and needs to be saved before activation", "formimpottip": "The version verification of the current form did not pass. Are you sure to discard unsaved modifications during import?", "exportformtip": "The version verification of the current form did not pass. Are you sure to discard unsaved modifications during export?", "templateparamstip": "After obtaining the cursor from the \"Title\" or \"Content\" input box on the left, click on the parameter below to automatically fill in the corresponding code segment", "timeouttip": "Set the effective time of a request, in seconds< Br>0 represents unlimited< If the request time is greater than 0, the requestheader needs to include key value pairs:<br>x-access date: timestamp", "qpstip": "The number of times the interface is allowed to access per second, in units of times per second< Br>0 represents unrestricted", "calldetailerror": "Failed to obtain call details", "maxnumbervalid": "The maximum value must not be less than or equal to the minimum value", "matrixtip": "Check the attributes that need to be displayed or searched on PC/mobile devices, and the display of search and display columns does not affect each other", "matirxhttpurl": "Please select the attribute in the matrix that saves the http or https link", "staticbatchdatatip": "1. Each row represents an option, and the values in each row cannot be duplicated<br/> 2. Values and names are separated by, and can only have values. Names will be automatically supplemented <br/> 3. Use the symbol - as a prefix to represent hierarchical relationships, and the number of - represents the number of levels, up to a maximum of 5 levels<br/>4. The array length cannot exceed 500.", "pastecelltip": "The current cell is not empty. Are you sure to replace the current content with the copied content?", "replacenewcomp": "The current cell is not empty. Do you want to replace it with a new component?", "clearcelltip": "Merging cells will clear the following components", "cusscenecomptip": "Customized scenarios only support selecting components from existing components in [main scenario]", "defultscenetip": "Customize the form scene, which can modify the form layout, hide, disable components, or modify component data sources based on the main scene components. Form scenarios can be associated with process nodes to achieve the same process, and the processing pages of different nodes display different form layouts. The viewing page of the work order uniformly displays the main scenario of the form.", "notdelscenetip": "The current form scene has been referenced and cannot be deleted", "forminputdatatip": "Help: Input data to test whether the form processes the data properly. The input data supports two formats: 1. Directly use the output data. 2. Simplified format: {ComponentUuid1: value1, ComponentUuid2: value2}", "formcostomhelp": "The template needs to comply with the Vue template syntax specification and must specify a root label, usually div, which supports all iView components and also supports the following internal components", "formcustomjshelp": "Configuration is a piece of JavaScript code that needs to define objects containing properties such as methods,Only supports ES5 syntax. such as", "selectcusconfig": "Select the basic configuration that the current custom component can use", "selectcusaction": "Select the interactive operations that can be used by the current custom component", "leastoneselectattr": "Please select at least one inherent attribute of the matrix", "staticrepeat": "There is duplicate data in the static data option, please edit again", "syncsceneattrtip": "The default scene contains attributes A, B, and C, and the current scene does not contain A, B, and C. Add the A, B, and C attributes to the current scene", "editwechattouser": "To be consistent with the user account in the user management of the enterprise WeChat backend", "regex": "Regular verification", "regextip": "Customize regular expressions to verify the validity of text box input; Do not verify when not filled in", "regularexpression": "regular expression", "regexvalidtip": "After the input verification fails, the user can re-enter according to this verification prompt", "regexvalidplaceholder": "The specific meaning of regular expressions, such as containing only numbers", "validtip": "Verification prompt", "datasourceselectmessage": "The referenced table input component has been deleted. Please reconfigure the attribute data source"}, "dashboard": {"templatehelp": "The template needs to comply with the vue template syntax specification, and must specify a root tag, typically a div, that supports all iView components. The data source results are returned in the form of an array, and the root attribute is: dataList. When accessing data, please directly iterate over the dataList to obtain the required data.", "confighelp": "Configuration is a piece of javascript code that requires defining objects that contain attributes such as methods and computed.", "imagehelp": "Only uploading images is supported, and the example of the referenced image path is:&lt; img&nbsp; src=&quot;&# 123; file:filename.png&#125;& quot;& sol;& gt;"}, "apierror": "Interface request error", "allimportedsuccessfullytotaltarget": "All imported successfully, total {target} items", "successfullyimportitemstarget": "Successfully imported {target} items", "failedtoimportitemstarget": "Import failed {target} items", "onlyasinglefilecanbeimported": "Only supports importing a single file", "thefilesuffixmustbe": "The file suffix must be", "singlefilescanbeuploaded": "Support for uploading individual files", "multiplefilescanbeuploaded": "Support uploading multiple files", "supportuploadingmultiplefileswithsuffixtarget": "Support uploading multiple suffixes as files", "supportuploadingsinglefileswithsuffixtarget": "Support uploading single files with suffix {target}", "mustbeanarray": "Must be an array", "virtualrootnode": "Virtual Root Node", "flattentreetip": "The second parameter of flattenTree must be a function with a return value", "providecorresponding": "Please provide the corresponding {target}", "issthplugin": "This is the {target} plugin", "targetnoexist": "{target} does not exist", "invalidjson": "Invalid JSON data", "nodenoexist": "The {target} node does not exist", "plugin": {"lineoutreject": "This node represents a refusal to connect the line!", "lineinreject": "This node represents a rejection of line connection!", "cannotlinein": "Unable to connect wirelessly", "cannotlineout": "Cannot be wired out", "nolinein": "No wires connected", "nolineout": "No wires connected", "notalonebystart": "Cannot be isolated by the starting node", "notalonebyend": "Cannot be orphaned by end nodes", "thisnode": "This node", "unknowfiletype": "Unable to determine the category of the uploaded file.", "cannotuploadfile": "Files that cannot be uploaded:", "cannotinsertpic": "Unable to insert picture at current position", "cannotresizeimgurl": "Unable to obtain image URL for resizing", "inputimgtitle": "Enter image title", "insertimgfail": "Failed to insert image", "italic": "tilt", "imgsizeresetfail": "Failed to select a resized image", "emptyurl": "The URL cannot be empty.", "linkhasnourl": "This link has no URL set", "mediaurlnotsupport": "This media URL is not supported.", "urlpastetips": "Tip: Pasting the URL into the content allows for faster embedding", "togglecaptionoff": "Close Table Title", "togglecaptionon": "Open Table Title", "correcttimerangedesc": "Please enter the correct time period in the format of 10:00-12:00, separated by multiple spaces", "serialnumberdesc": "Please enter the serial number, format example: 1 or 1. A or 1. A.1", "variablenamedesc": "Variable names can only consist of letters, numbers, underscores, and., and cannot start with a number", "donotinput": "Please do not enter&lt; &gt; &quot; &apos; Equal special symbols", "supporttype1": "Only Chinese characters, letters, numbers, and special characters (. _ -) are supported", "supporttype2": "Only letters, numbers, and special characters (. _ -) are supported", "supporttype3": "Only letters, numbers, slashes, and underscores are supported", "highriskcode": "There are high-risk codes present", "enName": "Only supports letters, numbers, and underscores"}, "incorrectformat": "Incorrect format", "accordingformat": "Please input according to the format requirements", "modifyfail": "Modification failed", "reportsuccess": "Successfully reported", "notsavedataupdateredirecttip": "Do you have unsaved data? Do you want to save the current modifications and jump to a new page?", "passcode": "Please enter a string with a length between 8-20, consisting of at least 2 combinations of letters, numbers, and special characters", "editaftersave": "Please save before editing", "commentsucces": "Comment successful", "syncsuccess": "Sync successful", "downloadsuccessful": "Download successful", "diagram": {"versionchecking": "The current version is currently under review and cannot be edited", "deletetemplateconfirm": "The current template is an automatically filled template. After deletion, the corresponding directory or configuration item will no longer automatically generate relevant architecture diagrams. Are you sure you want to delete it?", "deletetemplateanddiagramconfirm": "The current template is an automatically filled template with modification records. After deletion, the corresponding directory or configuration item will no longer automatically generate relevant architecture diagrams, and the modification records will also be deleted. Are you sure you want to delete it?", "deletecatalogconfirm": "After deleting the directory, all architecture diagrams and modification records under the directory will also be deleted. Are you sure?", "lockmessage": "The diagram is locked by {user} at {time}", "lockdiagramconfirm": "Are you sure to lock the current architecture diagram and enter editing mode?", "editingversionnotfound": "The current architecture diagram does not have a editing version", "noauthtoedit": "You do not have permission to edit the current architecture diagram", "isactivenotfound": "The current architecture diagram does not have an activated version", "editingversionirregular": "The current version is in a non editing state and cannot be edited"}, "runsuccess": "Execution successful"}