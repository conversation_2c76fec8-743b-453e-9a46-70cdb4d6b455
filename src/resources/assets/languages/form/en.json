{"label": {"name": "@:page.name", "icon": "@:page.icon", "animation": "Animation", "color": "@:page.color", "description": "@:page.description", "enable": "@:page.enable", "disable": "@:page.disable", "replycontent": "@:page.replycontent", "templatename": "@:page.templatename", "defaultcontent": "@:page.defaultcontent", "level": "@:page.level", "primarykey": "primary key", "desrequired": "Response is required"}, "help": {"nameattribute": "Name is used for topology display and relationship display", "uniquerule": "Select multiple attributes as the uniqueness check of configuration items", "pleaseselectnameattribute": "Please select an attribute as the name of the configuration item.", "activeautocollect": "Activate the automatic collection relationship to be configured in the collection mapping management", "upsideunique": "Unique means that the upstream end can only be referenced once by the downstream end", "downsideunique": "Unique means that the downstream end can only be referenced once by the upstream end", "deleteupside": "When deleting upstream configuration items, automatically delete downstream configuration items", "deletedownside": "When deleting downstream configuration items, automatically delete upstream configuration items", "cascaderrelation": "When the current relationship data changes, the system will automatically calculate and update the cascade relationship data. The calculated cascade relationship will not trigger the cascade relationship calculation again, nor will it generate transaction data, so it cannot be modified in the record backtracking.", "legalrule": "Model rules: configuration items that do not satisfy the relevant constraints of model attributes and relationships are considered non-compliant; custom rules: configuration items that meet custom rules are considered non-compliant.", "networksegment": "Input format: x.x.x.x/x", "multiport": "Multiple ports separated by commas", "community": "Group password or SNMPv3 authentication information, such as [\"public\",{\"username\":\"monitor\",\"authPassword\":\"AuthPassword\",\"authProtocol\":\"sha\",\"privPassword\":\"DataEncryptPassword\",\"privProtocol\":\"aes\"}]"}, "placeholder": {"name": "Please enter a name", "inputenchar": "Please enter English letters", "keyword": "Please enter a keyword", "pleaseselect": "Please select {target}", "pleaseinput": "Please enter {target}", "pleaseadd": "Please add {target}", "pleaseupload": "Please upload {target}", "required": "{target}(required)", "notrequired": "{target}(not required)", "checkrule": "check rule", "inputcolor": "Please enter a color", "searchtarget": "Search {target}"}, "validate": {"required": "{target} cannot be empty", "repeat": "{target} already exists, please re-enter", "fileformaterror": "The file format is incorrect", "fileformat": "The format of the file name {target} is incorrect.", "uploadfile": "Please upload the attachment", "fileoverlimit": "File size exceeds limit", "correctjson": "Please enter correct json", "addnode": "Please add a node", "adddatasource": "Please add a datasource", "pleaseenterthecontent": "Please enter the content", "englishnamerepetition": "English name repetition", "chinesenamerepetition": "Chinese name repetition", "validatefailed": "Validation failed", "pleaseinputscriptcontent": "Please enter script content", "requiredname": "Cannot be empty, only Chinese characters, letters, numbers and underscores are supported", "inputcorrectport": "Please enter the correct port", "inputtagdescription": "Please enter a tag description", "inputtagname": "Please enter a tag name", "selectprotocol": "Please select a protocol", "filecount": "Upload up to {target} files", "leastonetarget": "Please add at least one {target}", "completetheform": "Complete the form"}}