{"common": {"pagenoexist": "Page does not exist"}, "dashboard": {"dashboardmanage": "Dashboard Sets", "dashboardlist": "Dashboards", "dashboarddetail": "Dashboard Detail"}, "cmdb": {"customviewmanage": "Custom Views", "reltypemanage": "Relation Types", "alertlevelmanage": "Alert levels", "globalsearch": "Global Search", "searchcientity": "Configuration Item Query", "batchimport": "Batch Import", "transactionaudit": "Transaction Audit", "applicationlist": "Applications", "assetlist": "Assets", "tagmanage": "Tags", "accountmanage": "Accounts", "viewmanage": "View Sets", "discoverymanage": "Discovery Sets", "discoveryconfig": "Collector Sets", "discoveryjobmanage": "Discovery Jobs", "discoveryjobdetail": "Discovery job details", "discoverydata": "Auto Discovery Data", "unknowsmanage": "Unknown Device Management", "discoveryrulemanage": "Automatic discovery rule management", "cimanage": "Models", "globalattrmanage": "Global Attributes", "groupmanage": "Groups", "validatormanage": "Validators", "syncmanage": "collection configuration list", "cidetail": "Model Details", "ciview": "Configuration item list", "centityedit": "Configuration Item Edit", "centitybatchedit": "Configuration <PERSON><PERSON>", "centityview": "Configuration item details", "centitylist": "Query Configuration Items", "citopo": "Query Model Topology", "viewdata": "Data View", "viewdetail": "View Details", "viewedit": "Edit Data View", "applicationdetail": "Application List Details", "syncauditmanage": "Automatically collect sync records", "dsltest": "Automatically collect synchronous records", "inspectstatus": "Inspection status", "graphedit": "Edit topology view", "graphdata": "Topology View", "cientitymanage": "Configuration Items", "customview": "Custom View", "resourcecenter": "Resource Center", "discovery": "Auto Discovery", "cmdbmanage": "System Management", "objmanage": "Object Management"}, "process": {"workordercenter": "Work Center", "servicecatalog": "Service Catalog", "servicecatalogmanage": "Service Catalog Sets", "flowmanage": "Workflows", "nodemanage": "Nodes", "eventtypemanage": "Event Types", "eventsolutionmanage": "Solutions", "subtasktypemanage": "Subtask Policy", "prioritymanage": "Priority", "scoretemplatemanage": "Score Templates", "channeltypemanage": "Service Type Sets", "taskbatchdispatch": "Import", "changemanage": "Changes", "reltypemanage": "Relation Types", "replymanage": "Reply Templates", "taskdispatch": "Task dispatch", "taskdetail": "Ticket processing", "flowedit": "Flow Edit", "slamanage": "SLA Sets", "eventsolutionedit": "Edit Event Solution", "changeedit": "Change Edit", "eoatemplate": "EOA template", "eoatemplateedit": "EOA template editing"}, "autoexec": {"detail": "Automation is obvious", "job": "Job", "quickService": "Quick Service", "tool": "Tool", "config": "Configuration", "catalogmanage": "Catalog Management", "addjob": "Create job", "jobmanage": "Jobs", "timejob": "Scheduled jobs", "joblist": "Jobsist", "jobdetail": "Job Details", "combinationtool": "Combination Tool", "combinationtooldetail": "Combinationtooldetail", "toollibrary": "Tool Library", "customtoollibrary": "Custom Toollibrary", "operationlevel": "Operation Level", "toolclassification": "Tool Classification", "tooldirectory": "Tool Directory", "presetparameterset": "Preset parameter set", "globalparameter": "Global parameter", "scenedefinition": "Scene Definition", "customtemplate": "Custom Template", "editthebatchpublishjob": "Edit the batch publish job", "executebatchpublishjob": "Execute batch publish job", "executepublishjob": "Execute publish job", "scriptdetails": "Script details", "addscript": "Add script", "toollibrarydetail": "Toollibrarydetail", "customizetoolapproval": "Customize Tool Approval", "tooltestpage": "Tool Test Page"}, "framework": {"usermanage": "Users", "rolemanage": "Roles", "teammanage": "Teams", "authmanage": "Authority ", "useraddview": "Edit User", "worktimemanage": "Work Schedule Calendar", "usersetting": "Personal Settings", "roleaddview": "Edit Role", "authadduser": "Edit Authorization", "teamaddview": "Edit Group", "matrixmanage": "Matrixs", "matrixedit": "Matrix Edit", "formedit": "Form Edit", "formsceneedit": "Form Scene", "formmanage": "Forms", "integrationmanage": "Integration", "datawarehousemanage": "Data Warehouse", "datasource": "Data source", "historymanage": "Messages", "subscriptionsetting": "Subscribe", "noticemanage": "Notice", "mailserveredit": "Mail Server", "notifyconfigmanage": "Notification configuration management", "notifytacticsmanage": "Notification Tactics", "notifytacticsedit": "Notify Tactics Edit", "mqmanage": "Message Queue", "timingtaskedit": "Edit Timing Task", "tagentmanage": "Tagents", "thememanage": "Themes", "batchupgrade": "Batch Upgrade", "packagemanage": "Install package management", "runnergroupmanage": "Tagent Groups", "runnermanage": "Runner Management", "apimanage": "Interfaces", "auditmanage": "Audit", "jobmanage": "Scheduled Jobs", "filemanage": "Attachments", "fullTextIndexmanage": "Index management", "databaseView": "Rebuild database view", "modulemanage": "<PERSON><PERSON><PERSON>", "tenantconfigmanage": "Configuration information management", "threaddump": "Thread snapshot", "sqldump": "SQL Monitoring", "servermanage": "Server instance status", "databasefragment": "Database Fragmentation", "licensemanage": "Licenses", "batchoperation": "Batch Operation", "user": "Users and Roles", "message": "Messages and Announcements", "integration": "Data and Integration", "notify": "Notification and Subscription", "agent": "Agent & Runner", "others": "Basic Services", "healthcheck": "Health Check", "license": "License Management", "extramenu": "Menu management", "homepage": "default page ", "changelogaudit": "Database Change Log", "log": "view log"}, "knowledge": {"knowledgedetail": "entry details", "knowledgeedit": "Edit entry", "circlemanage": "Groups", "circleedit": "Edit Circle of Knowledge", "templatemanage": "Templates", "templateedit": "Edit template", "reviewdetail": "Review Details", "knowledgetypemanage": "Knowledge Type", "knowledgesearch": "Knowledge Search"}, "report": {"reportmanage": "Report Management", "reportdetail": "Report Details", "reporttemplatemanage": "Report Template Management", "sendplan": "Scheduled Delivery", "editsendplan": "Edit send plan", "screendesign": "Large Screen Designer", "screendetail": "Large screen details", "screenmanage": "Large screen management"}, "pbc": {"interfacedata": "Interface Data", "policymanage": "Policies", "executionrecordmanage": "Execution Record Manage", "interfacemappingmanage": "Interface Mapping Manage", "interfacemanage": "Interfaces", "propertymanage": "Propertys", "organizationsetting": "Organizations", "categorymanage": "Categories"}, "deploy": {"job": "job", "config": "Configuration", "applicationconfig": "Application Configuration", "versioncenter": "Version Center", "publishstatus": "Publish Status", "applicationconfigpipeline": "Application Configuration Pipeline", "combinationtooldetail": "Combinationtooldetail", "customizetooldetails": "Customizetooldetails", "tooldetail": "Tool Details", "tooltestdetail": "Tool Test Page", "jobdetail": "Job Details", "oneclickpublishing": "One-click publishing", "addpublishjob": "New Publish Job", "editbatchpublishjob": "Edit <PERSON><PERSON> Publish Job", "executebatchpublishjob": "Execute batch publish job", "pipelinedetail": "Pipeline", "pipelinemanage": "Superpipeline", "editpipeline": "<PERSON>", "activeversion": "Active Version", "schedulejob": "Scheduled job", "configadd": "Configuration", "addschedulejob": "Add scheduled job", "editschedulejob": "Edit scheduled job", "toolclassification": "Tool Classification", "webhook": "webhook"}, "inspect": {"inspectmanage": "Inspection Management", "inspectresult": "Inspection result", "latestproblem": "Latest Problem", "documentchangerecord": "Document Change Record", "inspectiondefinition": "Inspection Definition", "inspectiondefinitionedit": "Inspection Definition Edit", "assetinspection": "Asset Inspection", "applicationinspection": "Application inspection", "applicationlistdetails": "Application List Details", "applicationinspectiondetails": "Application inspection details", "inspectionjob": "Inspection job", "inspectionresult": "Inspection result", "problemreport": "Problem Report", "compositetooldetails": "Compositetooldetails", "jobdetails": "Job Details", "configurationitemdetails": "Configuration Item Details", "configurationitemediting": "Configuration Item Editing", "execute": "execute", "librarydetails": "Library Details", "tooltestpage": "Tool Test Page", "configurationfile": "configuration file", "thresholdrule": "<PERSON><PERSON><PERSON><PERSON> Rule", "thresholdruledetail": "Threshold rule details"}, "globalsearch": {"searchcenter": "Search Center"}, "rdm": {"projectdetail": "Project Detail", "projectmanage": "Project Management", "templatemanage": "Template Management", "projectconfig": "Project Config", "workbench": "Workbench", "mydoing": "To-Do List", "mycompleted": "Completed List", "prioritymanage": "Priority Management", "manage": "System Management", "myreported": "My Reported", "favorite": "My Favorited", "edittemplate": "Edit Template", "editdashboard": "Edit Dashboard"}, "codehub": {"codehub": "Code Center", "repositoryservice": "Code Library Service", "repository": "repository", "versiontype": "Version Type", "versioning": "Versioning", "versionstrategy": "Version Policy", "createmergerequest": "Create Merge <PERSON>", "handlermergerequest": "Handler Merge Request", "codereview": "Code Review", "mergerequestlist": "Merge Request List", "mergeactionmanage": "Merge Action Manage", "repositorydetail": "Repository Detail", "apilist": "Api List", "projectmapping": "Project Mapping", "repositoryvoucher": "Repository Voucher"}, "documentonline": {"documentonline": "Help Center", "documentdetail": "Document Details", "documentsearch": "Document Search", "directorymanage": "Document directory management"}, "dr": {"dr": "Disaster cutting", "basicsetting": "Basic Settings", "servicemanage": "service list", "servicedetail": "Service List Details"}, "diagram": {"diagrammanage": "System Management", "catalogmanage": "Catalog Management", "catalogdetail": "Catalog Detail", "diagramdetail": "Diagram Detail"}}