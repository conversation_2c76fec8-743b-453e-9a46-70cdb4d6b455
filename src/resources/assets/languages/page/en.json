{"actionaudit": "Action Record", "updatefrom": "Modification method", "apply": "Apply", "append": "Append", "model": "model", "change": "Change", "viewexample": "View example", "from": "from", "and": "and", "or": "or", "rule": "rule", "day": "day", "timer": "Timer", "detail": "Details", "cascadedelete": "Cascade delete", "add": "Add", "validating": "Data validation...", "readonly": "read only", "maintain": "Maintenance", "display": "Display", "distance": "Distance", "alertlist": "alert list", "attribute": "attribute", "action": "action", "require": "Required", "value": "value", "positiontransfer": "Position Swap", "creator": "Creator", "createtime": "Create Time", "memo": "Memo", "auth": "Assigned to", "useauth": "Use Authorization", "cover": "cover", "rollback": "Rollback", "backchange": "Back Change", "approve": "Approved", "error": "Error", "layout": "Layout", "success": "Success", "fail": "Failure", "relationtype": "Relation Type", "validate": "Validate", "isrequired": "Is required", "isunique": "Is it unique", "ishidden": "Hide?", "attributegroup": "Attribute Group", "autocollect": "Automatic collection", "moresetting": "More Setting", "basicinfo": "Basic Information", "createdate": "Created Date", "actiondate": "Action Date", "inputfrom": "Input method", "deletefrom": "Delete from", "deletetime": "Delete Time", "beforeedit": "Before modification", "afteredit": "After modification", "uniquekey": "Unique key", "build": "New", "new": "New", "alias": "<PERSON><PERSON>", "newtarget": "New {target}", "batchdelete": "Batch delete", "redo": "Redo", "color": "Color", "fontcolor": "Font color", "fontsize": "Font size", "backgroundcolor": "Background Color", "backgroundimage": "Background Image", "position": "Position", "margin": "<PERSON><PERSON>", "border": "Border", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "disable": "Disable", "edit": "Edit", "enable": "Active", "message": "message", "export": "Export", "exception": "Exception", "noexception": "No exception", "hasexception": "has exception", "checktime": "Check Time", "import": "import", "delete": "Delete", "save": "Save", "execute": "execute", "fullscreen": "Fullscreen", "group": "Group", "nogroup": "No grouping", "icon": "Icon", "language": "中文", "level": "level", "time": "time", "second": "second", "refresh": "Refresh", "refreshtarget": "Refresh {target}", "logout": "Logout", "ip": "IP", "name": "Name", "nodata": "No data", "notarget": "No {target}", "personalsetting": "Personal Setting", "priority": "Priority", "relation": "Relationship", "search": "Search", "status": "Status", "theme": "Theme", "themelight": "Light Theme", "themedark": "Dark Theme", "type": "Type", "warning": "warning", "others": "Others", "replytemplate": "<PERSON><PERSON>", "replycontent": "Reply content", "sourcesertype": "Source type", "targetsertype": "Target Type", "referencelist": "Reference List", "template": "Template", "templatelist": "Template List", "templatename": "Template name", "dimension": "{target} dimension", "fcu": "Editor", "fcd": "Edit time", "defaultcontent": "Default Response", "view": "View", "viewtarget": "View {target}", "viewmore": "View More", "deleteaudit": "Delete Record", "advancesearch": "Advanced Search", "saved": "{target} has been saved", "email": "email", "phone": "phone", "width": "width", "height": "height", "top": "top", "bottom": "bottom", "tag": "Tag", "title": "Title", "linkname": "Link name", "linktype": "Link Type", "subtitle": "Subtitle", "textmainbody": "text", "starttime": "Start time", "endtime": "End time", "catalogue": "catalogue", "rootdirectory": "Root Directory", "subdirectory": "Subdirectory", "copy": "Copy", "collect": "Favorite", "collected": "collected", "loadmore": "Click to load more", "loadfinish": "In the end", "setting": "Setting", "begin": "<PERSON><PERSON>", "finish": "End", "plantime": "plan time", "startstoptime": "startstoptime", "revocation": "Revocation", "recover": "Recover", "takeover": "Takeover", "downloadloadingtip": "Exporting data, please wait patiently", "param": "parameter", "verify": "Verify", "abort": "abort", "pause": "Pause", "reexecute": "Reexecute", "yes": "yes", "no": "No", "user": "User", "username": "Username", "userid": "User ID", "role": "Role", "classify": "Type", "classifytarget": "{target} classification", "rename": "<PERSON><PERSON>", "tip": "Tip", "authlist": "Authorization List", "whenandwhattodo": "at {time}{target}", "passed": "passed", "draft": "Draft", "submitted": "Pending review", "rejected": "Rejected", "viewauthority": "View Authority", "editauthority": "Edit Authority", "executeauthority": "Execute authority", "notificationstrategy": "Notification Strategy", "activity": "Activity", "versions": "versions", "hisversions": "Historical Versions", "curversions": "Current version", "accessory": "Accessory", "back": "Back", "reason": "reason", "failreason": "Failure Reason", "navigation": "Navigation", "update": "Update", "citetarget": "The current {target} has been cited and cannot be deleted", "nodeletetarget": "The current {name} has been referenced by {target} and cannot be deleted", "deletetarget": "The current {target} has been referenced, do you still want to delete it?", "global": "global", "viewdetails": "View Details", "target": "target", "filter": "Filter", "node": "node", "port": "port", "allowedit": "Allow editing", "environment": "Environment", "module": "module", "networkarea": "Network Area", "owner": "owner", "uploadtips": "Drag and drop files here or click to upload", "uploadtype": "Support uploading a single file with the suffix {target}", "uploadfilelimit": "Single file limit {target}MB", "uploadattachment": "Upload Attachment", "complete": "Complete", "help": "help", "colon": ":", "config": "Configuration", "test": "Test", "data": "data", "result": "Result", "find": "Find", "globalReplacement": "Global Replacement", "replacecontent": "Replace Content", "findcontent": "Find Content", "replacewith": "Replace with", "dataset": "Dataset", "itemname": "Object name", "itemtype": "Object Type", "inputtypename": "Please enter a type name", "table": "table", "field": "field", "whitelist": "whitelist", "blacklist": "Blacklist", "availableobject": "AvailableObject", "explain": "illustrate", "complexchange": "Complex Change", "withoutparam": "Without any parameters", "executeplan": "Execution Plan", "recipient": "Recipient", "emailtitle": "Email title", "cc": "Cc", "preview": "Preview", "length": "length", "input": "TextBox", "select": "Drop-down box", "multiselect": "Drop-down multi-select box", "checkbox": "checkbox", "radio": "radio box", "date": "date", "timerange": "Time Range", "datetimerangetext": "Year-Month-Day Hour: Minute", "daterangetext": "Year-Month-Day", "primarydirectory": "Primary directory", "secondarydirectory": "Secondary Directory", "bold": "Bold", "italic": "Italic", "strikeout": "strikeout", "orderedlist": "Ordered List", "unorderedlist": "Unordered list", "zh": "Chinese", "circle": "circle", "disc": "Black dot", "square": "square", "innerjoin": "Inner Join", "outerjoin": "Outer Join", "component": "Component", "image": "Image", "searchreplace": "Search Replace", "code": "code block", "link": "Link", "choose": "choose", "all": "all", "confirm": "Confirm", "clear": "clear", "advancedmode": "Advanced Mode", "simplemode": "Simple Mode", "keyword": "keyword", "protocol": "Protocol", "manufacturer": "Manufacturer", "condition": "condition", "filtercondition": "Filtercondition", "default": "default", "defaultvalue": "default value", "batchedit": "<PERSON>ch Edit", "move": "Move", "content": "Content", "clearall": "Clear all", "viewall": "View All", "number": "Number", "letter": "Letter", "lowercaseletter": "Lowercase letter", "capitalletter": "capital letter", "optional": "Optional", "clickandputaway": "Click to put away", "clicktoexpand": "Click to expand", "script": "<PERSON><PERSON><PERSON>", "previousstep": "Previous step", "thenextstep": "Next step", "compare": "Compare", "savedraft": "Save as draft", "submitaudit": "Submit Audit", "pleaseselect": "Please select", "insert": "Input", "source": "source", "download": "Download", "selectinput": "Select or input", "audit": "Audit", "reject": "Reject", "notrequired": "Not required", "englishname": "English name", "chinesename": "Chinese name", "viewtool": "View Tool", "unapprovedversion": "Unapproved Version", "limit": "Limit", "notlimit": "no limit", "quantitylimit": "Quantity Limit", "close": "close", "loading": "Loading...", "saving": "Saving...", "automaticscrolling": "Automatic scrolling", "lockscreen": "Lock Screen", "downloadlog": "Download Log", "downloading": "Downloading", "notauthrelationadmin": "No permission, please contact the administrator!", "copyscript": "Copy script", "addscript": "Add script", "limituser": "Available", "notify": "Notify", "activationtime": "Activation time", "filename": "filename", "task": "task", "tasklist": "Task List", "overtime": "timeout", "timeremaining": "remaining", "timecost": "Time cost", "loadingtip": "Loading", "userteam": "User Group", "score": "Score", "completetime": "Complete Time", "completed": "Completed", "paused": "Paused", "reply": "Reply", "inputparam": "Input parameter", "outputparam": "Output parameter", "file": "file", "read": "read", "write": "write", "size": "size", "authority": "authority", "displaysetting": "Display Setting", "customtemplate": "Custom Template", "updatetime": "Update time", "namedescription": "Name, description", "compile": "Compile", "compilecount": "Compile count", "normal": "normal", "publish": "Publish", "notpublish": "not published", "newversion": "New Version", "unittest": "Unit Test", "codescan": "Code Scan", "putawayall": "Put away all", "expandall": "Expand all", "scene": "Scene", "executeuser": "execute user", "inherit": "inherit", "fulllist": "All serial", "allparallel": "All Parallel", "notconfig": "Not configured", "path": "path", "exit": "Exit", "combinedcondition": "Combined condition", "basicatr": "Basic attributes", "formatr": "Form Attributes", "menu": "menu", "personal": "personal", "system": "system", "authuserroleteam": "Please authorize to user, group or role", "showtotal": "Show Total", "integratedtargetstars": "Integrated{target}stars", "custom": "Custom", "sort": "sort", "heavyload": "Heavyload", "responsibleperson": "Responsible person", "variable": "variable", "variablename": "Variablename", "variablevalue": "Variable value", "settonull": "set to null", "instance": "Example", "account": "Account", "database": "Database", "advancedsettings": "Advanced Settings", "filecoding": "File encoding", "password": "password", "jump": "Jump", "branch": "branch", "event": "Events", "logdetails": "Log Details", "selectall": "select all", "locked": "Locked", "wait": "wait", "resources": "Resources", "submit": "Submit", "cancel": "Cancel", "minute": "minute", "failurestrategy": "Failure Strategy", "successjudgment": "Success Judgment", "failurejudgment": "Failure judgment", "nextactivationtime": "Next activation time", "originalresults": "Original Results", "uploaduser": "Uploader", "uploadtime": "upload time", "attachmentname": "Attachment Name", "retreat": "Withdraw", "staging": "Staging", "transfer": "Transfer", "urge": "urge", "reapproval": "Reapproval", "revise": "Revise", "handle": "handle", "replytime": "Reply Time", "userrating": "User Rating", "valuation": "Evaluation", "open": "Open", "more": "More", "relevance": "Relevance", "selecttemp": "Select Template", "keepthecurrentpage": "Keep the current page", "button": "button", "referenceglobal": "Reference Global", "activitylist": "Activity List", "effectivenesstime": "effectiveness", "timeinterval": "Time Interval", "computationrules": "Computation Rules", "aftertimeout": "After timeout", "beforetimeout": "Before timeout", "positiveinteger": "positive integer", "triggercondition": "Trigger Condition", "text": "Text", "dateandtime": "datetime", "dropdownanoption": "Dropdown radio", "dropdownmultipleselection": "Dropdown multiple selection", "singleoption": "Single option", "check": "check", "filepath": "File Path", "textfield": "Textfield", "phase": "phase", "switch": "switch", "userselector": "User Selector", "lettersandnumbers": "Lettersandnumbers", "emailaddress": "Email Address", "phonenumber": "phone number", "ipaddress": "IP address", "defaultscenario": "<PERSON><PERSON><PERSON>", "history": "History", "ignore": "ignore", "reset": "Reset", "resetall": "Reset all", "deleteornot": "Delete or not", "warningmessage": "Warning message", "dataloadingcompleted": "Data loading completed", "constant": "Constant", "executionmode": "Execution Mode", "interval": "Interval", "hour": "hour", "object": "object", "naturalday": "Natural Day", "weekday": "weekday", "externaldata": "ExternalData", "noticesetting": "Notice Sets", "edittext": "Edit text", "formparams": "Form Parameters", "authsetting": "Authorization Setting", "mousedragsort": "<PERSON> Drag Sort", "usertype": "UserType", "teamtype": "Team Type", "roletype": "Role Type", "assets": "Assets", "hide": "<PERSON>de", "sourcecategory": "Source Category", "batchsearch": "Batch Search", "batchsearchvalue": "Batch Search Value", "saveasnewcategory": "Save as new category", "jobstatus": "Job Status", "mail": "mail", "before": "before", "member": "Member", "hierarchy": "Hierarchy", "timequantum": "Time Quantum", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "7", "august": "August", "september": "September", "october": "October", "November": "November", "december": "December", "defaultmodule": "<PERSON><PERSON><PERSON>", "homepage": "Homepage", "borderwidth": "Border width", "bordercolor": "Border color", "informant": "Importer", "reportingtime": "Reporting time", "directorydescription": "Directory Description", "workordernumber": "Work order number", "actions": "actions", "referenceinputparameter": "Reference input parameter", "lowerdirectory": "Lower Directory", "matrix": "matrix", "style": "style", "leftalign": "Left", "centeralign": "Center", "rightalign": "Right", "invokecount": "Reference Count", "pending": "ready", "notbuild": "not built", "slowest": "Slowest", "fastest": "fastest", "lessest": "least", "most": "most", "networksegment": "network segment", "snmpport": "SNMP port", "community": "community character", "speed": "speed", "thread": "thread", "record": "Record", "datacapacity": "Data capacity", "ready": "Ready", "undefined": "undefined", "invokeformcomponent": "Associated form component", "inittime": "Initialized time", "map": "map", "copywriting": "copywriting", "col": "Column", "lightcolor": "Light Color", "darkcolor": "dark color", "englishonly": "Only English letters are supported", "importicon": "Import icon", "conditionnotnull": "Condition is not empty", "definition": "definition", "interface": "Interface", "showalias": "Show aliases", "packup": "Put away", "previouspage": "previous page", "nextpage": "next page", "advanceconfig": "Advanced Configuration", "valueconvert": "Value conversion", "mapping": "mapping", "primarykey": "Primary key", "animation": "animation", "desrequired": "Need Reply", "databasestatus": "Database Status", "autorefresh": "auto refresh", "manualrefresh": "Manual Refresh", "ms": "millisecond", "tenant": "tenant", "rebuildtable": "Rebuild Table", "tablename": "Table Name", "engine": "engine", "linenum": "Number of rows", "batchoperation": "<PERSON><PERSON>", "wordbreaklist": "Participle result", "importdata": "Import Data", "importfile": "Import File", "globalupdate": "Global Update", "onlyadd": "Add only", "onlyupdate": "Update only", "addupdate": "Add and Update", "frompage": "Page modification", "fromexcel": "Import from Excel", "fromitsm": "Process modification", "commitdate": "Submission Date", "restoredate": "Recovery Date", "committime": "Submission time", "errorornot": "Abnormal state", "procotolmanage": "Protocol Management", "valuechange": "Value change", "lcu": "modification date", "importaudit": "Import Record", "uploadimportfile": "Upload Import File", "componentconfig": "Component Configuration", "tagmanage": "Label management", "exportinitdata": "Export initial data", "importinitdata": "Import initial data", "rebuildindex": "Reindex", "indexcount": "Number of indexes", "rebuildstartime": "Reconstruction start time", "rebuildendtime": "Rebuild End Time", "execcount": "Number of executions since startup", "keeprecords": "Keep records", "nextfiretime": "Next activation time", "log": "journal", "serverid": "Server ID", "attrname": "Attribute Name", "attrtype": "Attribute Type", "attrvalue": "Attribute Value", "jobgroupname": "Job group name", "jobname": "Job Name", "classpathname": "Classpath", "actiontype": "Operation type", "returncode": "Return code", "errorcode": "Error code", "running": "Under execution", "successed": "Successfully", "failed": "Failed", "deletewarning": "Delete Warning", "enumerate": "enumeration", "isdefault": "Default?", "customapi": "External interface", "systemapi": "Internal interface", "address": "address", "handler": "processor", "visittimes": "Number of visits", "authtype": "Authentication method", "needaudit": "Enable auditing", "notneedaudit": "Audit not enabled", "isneedaudit": "Enable auditing", "formname": "Form Name", "isactived": "Activated", "unactived": "not active", "react": "linkage", "textfontsize": "Text font size", "textfontcolor": "Text Font Color", "numberfontsize": "Number font size", "numberfontcolor": "Number Font Color", "padding": "spacing", "borderradius": "Border radian", "sysnotice": "System announcement", "allmarkread": "<PERSON>", "prev": "Previous", "next": "Next", "iknow": "I got it!", "today": "today", "yesterday": "yesterday", "msgtype": "Message Type", "shortshow": "temporary", "longshow": "continued", "popreminder": "<PERSON><PERSON>", "subscriptionstatus": "Subscription Status ", "notifyobj": "Notification object", "stopuse": "Deactivate", "issued": "Issued", "issuedtime": "Distribution period", "ignoreread": "Ignore Read", "noticeissued": "Announcement issuance", "announcement": "Notice", "matrixname": "Matrix Name", "datasourcetype": "Data source type", "referencecount": "Number of references", "reference": "quote", "exporterror": "Export failed", "row": "that 's ok", "notsetting": "Do not set", "form": "form", "attrsmanage": "Attribute Sets", "cipher": "secret key", "server": "The server", "domain": "domain name", "scheduledtask": "Scheduled task", "searchformore": "Search for more", "backgroundopacity": "Transparent background", "allofthem": "All", "in": "to", "created": "establish", "operatewithoutpermission": "Operation without permission", "inaddition": "and", "belong": "belong to", "pagenotvalid": "Page does not exist", "method": "method", "targetaddress": "Destination Address", "authentication": "authentication", "paramdesc": "Parameter Description", "othersetting": "Other settings", "key": "key", "defaultport": "Default Port", "sitenavigation": "Site Navigation", "week": "week", "month": "month", "year": "year", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "firday": "Friday", "saturday": "Saturday", "notdefine": "Do not specify", "every": "every", "minu": "minute", "hou": "hour", "da": "day", "datasource": "datasource", "currentvalue": "Current value", "dbtype": "Database Type", "effectivetime": "Effective time", "multipleselection": "multiple selection", "copyall": "Copy All", "paramslist": "parameter list", "clearconfig": "Clear Configuration", "resetpwd": "reset password ", "upgrade": "upgrade", "allstatus": "All statuses", "groupname": "Group Name", "uploadfile": "Upload file", "package": "Installation package", "replace": "replace", "updatepwd": "Update Password", "revert": "reduction", "order": "order", "fieldname": "Column Name", "appname": "apply name", "continue": "continue", "continueexecute": "Continue execution", "retry": "Retry", "deleteandcommit": "Delete and commit", "savetransaction": "Save Transaction", "savecommittransaction": "Save transaction and commit", "enabled": "Enable", "startplan": "Plan Start", "finishplan": "Plan End", "ban": "disabled ", "inquire": "query", "clearup": "clear", "unselectall": "Deselect All", "welcomeimg": "Welcome Image", "welcometo": "Welcome to", "selected": "Selected", "unselected": "Unchecked ", "one": "one", "two": "two", "three": "three", "four": "four", "five": "five", "six": "six", "particularyear": "particular year", "batchselect": "Batch selection", "calendar": "calendar", "abandon": "give up", "nonexistent": "non-existent", "list": "list", "intotaltarget": "{target} in total", "itemspagetarget": "{target} items/page", "homepagesettings": "Home page settings", "returntohomepage": "Return to homepage", "filepreviewfailed": "File preview failed", "formatfilepreviewiscurrentlynotsupportedtarget": "{target} format file preview is currently not supported", "lastday": "Last Day", "lastweek": "Last week", "lasthalfmonth": "Last Half Month", "last3month": "Last 3 month", "lastmonth": "Last month", "lastsixmonths": "Last 6 months", "lastyear": "Last year", "eg": "Example", "count": "quantity", "plugins": "plugin unit", "completrate": "Completion rate", "black": "black", "blue": "blue", "green": "green", "grey": "grey", "hyperlink": "Hyperlink", "orange": "orange", "paragraph": "paragraph", "purple": "purple", "red": "red", "white": "white", "yellow": "yellow", "particularmonth": "month", "dataloading": "Loading data", "alphabet": "English letter", "chinesecharacter": "chinese characters", "cidradress": "CIDR address", "nothave": "nothing", "token": "user token ", "request": "request", "sendrequest": "Send Request", "outputresults": "Output Results", "requestmethod": "Request Method", "servicelife": "Usage period", "inputtip": "Input prompt", "displayformat": "Display Format", "filetemplate": "Attachment Template", "uploadtemplace": "Upload Template", "onefile": "Individual attachments", "morefile": "Multiple attachments", "disabledselect": "Not selectable", "rootnode": "Root node", "later": "After", "earlier": "Before", "option": "option", "strlength": "Character length", "tab": "tab ", "line": "Horizontal line", "card": "card", "fieldmapping": "Field Mapping", "dropdownoption": "Dropdown option", "displaytext": "Display Text", "ratenumber": "Total number of stars", "vertical": "portrait", "horizontal": "transverse", "numberrange": "Number range", "minnumber": "minimum value", "maxnumber": "Maximum value", "decimalnumber": "Decimal places", "end": "ending", "right": "Keep to the right", "left": "Left", "solid": "Solid line", "dashed": "Dashed line", "contentPosition": "Text Position", "dividderwidth": "line width", "css": "style", "category": "category", "easymode": "Easy mode", "accordionmode": "Accordion mode", "panel": "panel", "dragsort": "Drag Sort", "pagesize": "Number of entries per page", "integration": "integrate", "format": "format", "staticdatasource": "Static Datasource", "eachother": "interactive", "ordernumber": "Serial number", "invisible": "invisible", "small": "Small", "medium": "Medium", "big": "large", "maximum": "maximum", "merge": "merge", "cancelmerge": "<PERSON><PERSON>", "clearcontent": "Clear Content", "clearstyle": "Clear Format", "paste": "paste", "cut": "shear", "scenarioname": "Scenario Name", "updatedata": "Update data", "triggeraction": "Trigger Action", "triggertime": "Trigger Time", "triggerevent": "Trigger Event", "emit": "trigger", "filters": "filter", "changehandler": "Switch components", "isreadonly": "Read Only", "isdisabled": "Is it disabled", "ismask": "Is it not visible", "grouptype": "Group Type", "thead": "Meter header", "showvalue": "Display value", "avatar": "avatar", "public": "public", "upload": "upload", "restart": "<PERSON><PERSON>", "times": "second", "acessdenine": "No access rights", "login": "Sign in", "accountorpwderror": "Account or password error", "comment": "comment", "notsave": "Do not save", "valuefield": "Value Field", "portnumber": "Port number", "datasaving": "Saving data", "detailinfo": "detailed information", "remainingtime": "Remaining time", "contentdetail": "Content Details", "timeline": "Timeline", "summaryinfo": "Summary Info ", "testconnection": "Test Connection", "accountsmanage": "Account Management ", "publicaccount": "Public account", "privateaccount": "Private account", "fixedpage": "Fixed Page", "cancelfixedpage": "Unpin Page", "autoexec": "automation", "buttontype": "Button type", "background": "background", "use": "purpose", "clicksetting": "Click on Settings", "autoexecjob": "Automated operations", "foreachtable": "Traverse Table", "personalizationsettings": "Personalization settings", "current": "current", "hasfile": "With attachments", "nofile": "No attachments", "synchronous": "synchronization", "switchnode": "Switch nodes", "subsystem": "Subsystem", "manualcreation": "Manual creation", "warehouseaddress": "Warehouse address", "trunk": "trunk", "versiontype": "Version Type", "browse": "browse", "voucher": "voucher", "sourcebranch": "Source Branch", "targetbranch": "Target Branch", "keyname": "Key name", "necessary": "essential", "viewparameters": "View parameters", "conflict": "conflict", "detailcontent": "Detailed content", "presenter": "submitter", "submitinformation": "Submit Information", "effectiveness": "Validity", "strip": "strip", "globalsearch": "global search ", "maxlength": "Maximum length", "forceflush": "force refresh ", "personnel": "personnel", "isexpired": "Timed out or not", "nottimedout": "Not timed out", "namepre": "name prefix ", "total": "Total", "activesucess": "Activation successful", "unactivesuccess": "Disabled successfully", "userdisabled": "User disabled", "userdelete": "User deleted", "iselected": "Selected", "importconfirm": "Import Confirmation", "startdate": "Start date", "enddate": "End date", "moveup": "Move Up", "movedown": "Move Down", "nostatus": "Stateless", "customscript": "Custom Script", "scripthelp": "Script Description", "componentinfo": "Component Information", "apimethod": "API method", "scripterror": "Script syntax exception", "wee": "week", "sum": "total", "wechat": "WeCom", "updateuser": "Updated by", "norelevantdocuments": "There are currently no relevant documents available", "viewalldocuments": "View all documents", "counter": "frequency", "compareexpression": "Comparative operation", "logicalexpression": "Logical operation", "batchpause": "Batch pause", "batchabort": "Batch cancellation", "ismultiple": "Do you want to select multiple options", "homeurl": "Application Service Address", "filltype": "Background Fill Method", "backgroundresizable": "Background Follow Zoom", "auto": "automatic", "foreachobj": "Traverse Object", "mainscene": "Main scene", "enter": "enter", "viewdata": "View Data", "you": "you", "highriskcode": "High risk code", "importoverdescrition": "The following associated objects already exist in the system. If you want to overwrite them, please check the objects you want to overwrite and click the [Import] button below to complete the import.", "uploadurlmustrequired": "The upload address cannot be empty", "existiscoverimport": "{type}【{target}】 already exists. Do you want to continue importing and overwrite it", "userauthfailedpleaselogin": "authentication failed, please log in", "partial": "local", "rolerule": "The request for login authentication needs to carry a header as a rule expression. If the value of the expression after execution is true, the role will take effect, otherwise it will not take effect. For example: \"${DATA.env}\"==\"sit\"ß&&(\"${DATA.test}\"=\"1\" | | \"${DATA.test2}\"==\"aaa\")", "datasources": "data sources", "formcomponents": "Form component", "agree": "agree", "autoexeccombopeditinfooptypetip": "Query class: Only assets with read-only permissions in the resource center can be used as execution targets; Operations: Only assets with 'automated operations' permissions in the resource center and group management can be used as execution targets;", "autoexeccomboptype": "Tool classification", "autoexecchecktagentstatus": "Confirm the connection status of the agent", "clicktoupload": "Click to upload", "dragfilehere": "Please drag the attachment here", "clickanddragfile": "Click or drag to upload", "Initiate": "launch", "opinions": "view", "menuname": "<PERSON>u Name", "acolumn": "One column", "hiddenattr": "Hide Properties", "dynamicvalue": "Dynamic value", "exitedit": "Exit Editing", "notype": "Typeless", "autoexeccomboprunnergrouptips": "Only effective for stages where the execution method is \"runner execution\"", "autoexeccomboprunnergrouplabel": "Runner Execution Group", "autoexecradomrunnergroup": "Random allocation", "common": "ordinary", "design": "design", "licensedberror": "Please contact the administrator to verify the value of the db.URL parameter in the system configuration and authorize again", "licenseexpired": "Invalid", "licenseend": "Terminated", "workcenternoauthbatchdelete": "The following work order : {target}, does not have permission to delete", "nocommon": "There is currently no explanation", "deploynoexecuteconfigauthtip": "You do not have the \"execute or edit configuration permissions\" for the current application. Please contact the administrator for authorization", "deploy": {"contactwithadmin": "Please contact the administrator for authorization"}, "autoexecwaitinputnoauth": "Currently not authorized '{target}', please contact administrator for assistance", "defaultthead": "Default header", "workcenterresettheadsetting": "Do you want to restore the default configuration", "workcentertitleresettheadsetting": "Restore configuration", "resetconfirm": "Restore Confirmation", "nocomment": "There is currently no description available", "cleardata": "wipe data ", "customdata": "Custom data", "clicktoselect": "Click to select", "mappingmode": "Mapping mode", "delayed": "Delayed", "installtime": "Packaging date", "orgnization": "organization", "isdefaultselectd": "Is it selected by default", "defaultselectdonlyvalue": "When there is only one option that is not required, it is selected by default", "userclearsessioncache": "Clear user session cache", "lastfire": "Finally activated", "lastfinish": "Finally completed", "nextfire": "Next activation", "jobtime": "Homework time", "currenttime": "current time ", "front": "front", "after": "after", "includesdata": "There is data available", "isfontbold": "Is the font bolded", "nochange": "unchanged", "licenseexception": "License exception", "license": "permit", "favicon": "Website icon", "autoexeccomboprunnergrouptagtips": "The actuator group needs to meet the current label requirements", "disabledd": "Prohibit adding", "disabledelete": "Prohibit deletion", "notbelong": "Not belonging to", "autoexecservice": "Automated services", "unifiedauthentrance": "Unified authentication entrance", "tagentregistercount": "Tagent registration times", "term": {"prev": "the previous", "next": "next", "printagent": "Reprint", "rank": "weight", "alived": "survival"}, "uniquenotedit": "The unique identifier cannot be modified after being set", "formstyle": "form sheet ", "cellspacing": "Cell spacing", "dragrow": "Drag and drop line", "getscreenshotfromclipboard": "Take a screenshot from the clipboard", "getscreenshotfromclipboarddesc": "Click this button to retrieve a screenshot from the clipboard again (which will replace the current screenshot).", "uploadscreenshot": "Upload screenshot", "uploadingscreenshot": "Screenshot uploading", "screenshotpreview": "Screenshot Preview", "screenshotname": "Screenshot Name", "screenshot": "screenshot", "errorreadingclipboarddata": "Error reading clipboard data", "noimageresourcesfoundintheclipboard": "There are no image resources in the clipboard", "nodatafoundintheclipboard": "There is no data in the clipboard", "sendsucceed": "<PERSON><PERSON> successfully", "pleasepastescreenshotfromtheclipboard": "Please paste a screenshot of the clipboard", "pastethescreenshotfromtheclipboardintotheinputboxbelow": "Copy and paste the screenshot of the clipboard into the input box below:", "pastethescreenshotfromtheclipboard": "Paste clipboard screenshot", "scriptlanguage": "scripting language", "example": "for example", "copyerrorinfo": "Copy error message", "loadingfailed": "<PERSON><PERSON>", "topo": "Topology Map", "pastehere": "Paste here", "dragthecanvas": "Drag and drop canvas", "rubberband": "Frame Select", "actualpath": "actual path", "currentjob": "Current Job", "incompleteinformation": "incomplete information", "requiredparameterhasnullvalue": "The required parameter field has a null value", "executeall": "Execute all", "cache": "<PERSON><PERSON>", "connectionsuccessful": "Connection successful", "driverjar": "Driver jar package", "ignored": "<PERSON>eg<PERSON>", "loaded": "Loaded", "dictionary": "dictionary", "healthcheckresult": "Inspection results", "autoexecparallpolicy": "Concurrency Strategy", "autoexecbatchround": "in batches", "autoexecparall": "Concurrency", "autoexecparallel": "Concurrent settings", "paramkeyword": "Participate in search", "loglevel": "log level", "bluegreen": "Blue green execution", "mailserver": "mail serve"}