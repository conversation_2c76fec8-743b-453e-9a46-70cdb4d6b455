import formcheckbox from './select';//w
import formdate from './date';//w
import formtime from './time';//w
import formradio from './select';//w
import formselect from './select';//w
import formuserselect from './userSelect.vue';
import forminput from './input'; //o
import formtextarea from './input';
import formeditor from './input';
import formcascadelist from './cascadeList';
import formcascadeitem from './cascadeItem';

export default {
  formcheckbox,
  formdate,
  forminput,
  formradio,
  formselect,
  formtextarea,
  formtime,
  formeditor,
  formuserselect,
  formcascadelist,
  formcascadeitem
};
