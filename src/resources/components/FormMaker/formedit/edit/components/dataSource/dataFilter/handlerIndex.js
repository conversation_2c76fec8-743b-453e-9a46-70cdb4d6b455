import textattr from '@/views/pages/cmdb/ci/attrhandler/search/text-attr.vue';
import selectattr from '@/views/pages/cmdb/ci/attrhandler/search/select-attr.vue';
//import radioattr from '@/views/pages/cmdb/ci/attrhandler/search/radio-attr.vue';
import tableattr from '@/views/pages/cmdb/ci/attrhandler/search/table-attr.vue';
import textareaattr from '@/views/pages/cmdb/ci/attrhandler/search/textarea-attr.vue';
//import urlattr from '@/views/pages/cmdb/ci/attrhandler/search/url-attr.vue';
//import checkboxattr from '@/views/pages/cmdb/ci/attrhandler/search/checkbox-attr.vue';
import dateattr from '@/views/pages/cmdb/ci/attrhandler/search/date-attr.vue';
import timeattr from '@/views/pages/cmdb/ci/attrhandler/search/time-attr.vue';
import datetimeattr from '@/views/pages/cmdb/ci/attrhandler/search/datetime-attr.vue';
import datetimerangeattr from '@/views/pages/cmdb/ci/attrhandler/search/datetimerange-attr.vue';
import fileattr from '@/views/pages/cmdb/ci/attrhandler/search/file-attr.vue';
import passwordattr from '@/views/pages/cmdb/ci/attrhandler/search/password-attr.vue';
import numberattr from '@/views/pages/cmdb/ci/attrhandler/search/number-attr.vue';
import expressionattr from '@/views/pages/cmdb/ci/attrhandler/search/expression-attr.vue';
import enumattr from '@/views/pages/cmdb/ci/attrhandler/search/enum-attr.vue';
import hyperlinkattr from '@/views/pages/cmdb/ci/attrhandler/search/hyperlink-attr.vue';
export default {
  textattr,
  selectattr,
  // radioattr,
  tableattr,
  textareaattr,
  // urlattr,
  // checkboxattr,
  dateattr,
  timeattr,
  datetimeattr,
  datetimerangeattr,
  fileattr,
  passwordattr,
  numberattr,
  expressionattr,
  enumattr,
  hyperlinkattr
};
