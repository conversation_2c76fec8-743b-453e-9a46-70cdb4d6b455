<template>
  <div ref="container"></div>
</template>
<script>
import { Bar } from '@antv/g2plot';
import { WidgetBase } from '../widget-base.js';

export default {
  name: '',
  components: {},
  extends: WidgetBase,
  props: {
  },
  data() {
    return {
      plot: null,
      chartConfig: {
        autoFit: true,
        xField: 'xField',
        yField: 'yField',
        legend: { visible: true, position: 'bottom' },
        color: this.getChartTheme('chart'),
        label: { 
          position: 'right',
          visible: true,
          offset: 0, // 设置偏移量为0，柱状图和文本标签距离不需要太远
          layout: [
            {
              type: 'limit-in-plot' // 限制文本标签在图形范围内，可以防止文本标签于图表组件如坐标轴发生重叠遮挡
            }
          ],
          style: {
            fill: this.widget.config.label?.style?.fill || this.getChartTheme('textColor')
          }
        },
        statistic: {
          visible: true
        },
        meta: {
          xField: {
            alias: this.widget.config.xAxis?.title?.text || this.$t('term.report.axis.xaxistitle')
          },
          yField: {
            alias: this.widget.config.yAxis?.title?.text || this.$t('term.report.axis.yaxistitle')
          }
        }
      }
    };
  },
  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {
  },
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {
  },
  destroyed() {},
  methods: {
    createRandomData() {
      this.data = [];
      for (var i = 1; i <= 10; i++) {
        this.data.push({yField: this.$t('page.data') + i, xField: Math.floor(Math.random() * 100) + 1});
      }
    },
    createPlot() {
      if (this.plot) {
        this.plot.destroy();
        this.plot = null;
      }
      if (this.$refs.container) {
        this.plot = new Bar(this.$refs.container, {
          ...this.chartConfig,
          data: this.finalData
        });
        this.plot.render();
      }
    },
    changeData() {
      if (this.plot) {
        this.plot.changeData(this.finalData);
      }
    }
  },
  filter: {},
  computed: {
    finalData() {
      const data = [];
      if (this.data && this.data.length > 0) {
        //时间排序
        this.data = this.data.sort((a, b) => this.changeString(b.yField) - this.changeString(a.yField));
        //数据合并汇聚
        this.data.forEach(d => {
          const dd = data.find(dd => dd.yField == d.yField);
          if (dd) {
            dd.xField += d.xField;
          } else {
            data.push(d);
          }
        });
      }
      return data;
    }
  },
  watch: {
  }
};
</script>
<style lang="less"></style>
