export { default as textwidget } from './original/text-widget.vue';
export { default as numberwidget } from './original/number-widget.vue';
export { default as tablewidget } from './original/table-widget.vue';
export { default as piewidget } from './original/pie-widget.vue';
export { default as radarwidget } from './original/radar-widget.vue';
export { default as rosewidget } from './original/rose-widget.vue';
export { default as columnwidget } from './original/column-widget.vue';
export { default as barwidget } from './original/bar-widget.vue';
export { default as linewidget } from './original/line-widget.vue';
export { default as mlinewidget } from './original/mline-widget.vue';
export { default as groupedcolumnwidget } from './original/groupedcolumn-widget.vue';
export { default as stackedcolumnwidget } from './original/stackedcolumn-widget.vue';
export { default as areawidget } from './original/area-widget.vue';
export { default as mareawidget } from './original/marea-widget.vue';
export { default as gaugewidget } from './original/gauge-widget.vue';
export { default as liquidwidget } from './original/liquid-widget.vue';
export { default as pscatterwidget } from './original/pscatter-widget.vue';
export { default as scatterwidget } from './original/scatter-widget.vue';
export { default as heatmapwidget } from './original/heatmap-widget.vue';
export { default as funnelwidget } from './others/funnel-widget.vue';
export { default as wordcloudwidget } from './others/wordcloud-widget.vue';
export { default as stackedbarwidget } from './original/stackedbar-widget.vue';
export { default as bulletwidget } from './original/bullet-widget.vue';
export { default as columnlinewidget } from './original/columnline-widget.vue';

export { default as mapareawidget } from './geography/maparea-widget.vue';
export { default as districtwidget } from './geography/district-widget.vue';
export { default as pointwidget } from './others/point-widget.vue';
export { default as customwidget } from './others/custom-widget.vue';
export { default as timewidget } from './others/time-widget.vue';
