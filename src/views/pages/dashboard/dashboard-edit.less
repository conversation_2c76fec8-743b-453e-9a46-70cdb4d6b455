@import (reference) '~@/resources/assets/css/variable.less';
.theme(@default-dividing, @white, @white1, @default-background, @actionItem, @bar,@primary-color) {
  .dashboard-edit-container {
    .tscontain-right {
      background-color: transparent;
      border-left: 1px solid @default-dividing;
    }

    .tscontain-header {
      border-bottom: 1px solid @default-dividing;
    }
    .span-black{
      display: inline-block;
    }
  }

  .dashboard-name {
    display: inline-block;
    line-height: 30px;
    padding: 0px 15px;
    width: 350px;
    padding-left: 0;
  }

  .dashboard-item {
    padding: 0 4px;
    &.vue-grid-item {
      > .vue-resizable-handle {
        bottom: -4px;
      }
    }

    /*去掉拖动时不会闪 &:hover {
      z-index: 2;
    }*/
  }

  .dashboardTreeList {
    padding: 8px 18px;
    .ivu-tree-title {
      color: @bar;
    }
    .ivu-tree-title:hover {
      background-color: transparent !important;
    }

    .ivu-tree-children > li {
      > .ivu-tree-children {
        padding-right: 18px;
        margin: 10px 0;
        > li {
          > .ivu-tree-arrow {
            display: none;
          }
          background-color: @white;
          height: 58px;
          line-height: 58px;

          .icon {
            font-size: 18px;
            vertical-align: middle;
            margin-left: 26px;
            margin-right: 26px;
          }

          span {
            vertical-align: middle;
          }
        }
      }
    }
    .ivu-tree-title {
      width: 100%;
    }
  }

  .chart-edit-container {
    display: flex;
    min-height: 536px;
    height: 100%;
    //margin: -12px -16px;
    background-color: @white1;
    .left {
      width: 180px;
      border-right: 1px solid;
      padding: 8px 0;
    }

    .main {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .preview {
      height: 100%;
      background-color: @default-background;
      overflow: hidden;
    }

    .ivu-split-trigger {
      background-color: @default-background;
      .ivu-split-trigger-bar {
        background-color: @bar;
      }
    }

    .operation {
      flex: 1;
      height: 100%;

      .options {
        height: 42px;
        line-height: 48px;
        border-bottom: 1px solid;
        padding: 0 23px;

        .item {
          float: left;
          height: 100%;
          margin-right: 32px;
          cursor: pointer;
        }

        .active {
          border-bottom: 2px solid @primary-color;
          color: @primary-color;
        }
      }

      .content {
        padding: 0 23px;
        height: calc(100% - 42px);
        overflow: auto;
      }
    }
  }

  .chart-type-list {
    .item {
      width: 156px;
      height: 36px;
      border-top-right-radius: 18px;
      border-bottom-right-radius: 18px;
      padding-left: 25px;
      line-height: 36px;
      cursor: pointer;

      span {
        vertical-align: inherit;
      }
    }

    .active {
      background-color: @actionItem;
    }

    .icon {
      /*display: inline-block;*/
      margin-right: 16px;
    }
  }
}

html {
  .theme(@default-dividing, @white, @white, @default-background, @default-dividing, @default-text,@default-primary-color);

  &.theme-dark {
    .theme(@dark-dividing, @dark-dividing, @dark-op, @dark-blockbg, @dark-blockbg, @dark-text,@dark-primary-color);
  }
}
