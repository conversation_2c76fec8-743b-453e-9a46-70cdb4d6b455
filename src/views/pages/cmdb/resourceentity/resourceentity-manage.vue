<template>
  <div>
    <Loading :loadingShow="loadingShow" type="fix"></Loading>
    <TsContain>
      <template v-slot:topLeft>
        <span class="text-action tsfont-plus" @click="addData()">{{ $t('term.cmdb.view') }}</span>
      </template>
      <template v-slot:topRight>
        <InputSearcher v-model="keyword"></InputSearcher>
      </template>
      <template v-slot:content>
        <TsTable v-if="tbodyList" :theadList="theadList" :tbodyList="finalTbodyList">
          <template v-slot:name="{ row }">
            <a @click="editEntity(row)">{{ row.name }}</a>
          </template>
          <template v-slot:status="{ row }">
            <span v-if="row.status === 'error'" class="text-error">{{ $t('page.exception') }}</span>
            <span v-if="row.error">
              <Poptip :transfer="true" placement="right" trigger="hover">
                <i class="tsfont-warning-s text-error"></i>
                <div slot="content">
                  {{ row.error }}
                </div>
              </Poptip>
            </span>
            <span v-else-if="row.status === 'ready'" class="text-success">{{ $t('page.pending') }}</span>
            <span v-else-if="row.status === 'pending'" class="text-grey">{{ $t('page.notbuild') }}</span>
          </template>
          <template v-slot:action="{ row }">
            <div class="tstable-action">
              <ul class="tstable-action-ul">
                <li v-if="row.isMultiple" class="tsfont-copy" @click="copyEntity(row)">{{ $t('page.copy') }}</li>
                <li v-if="row.isMultiple" class="tsfont-trash-o" @click="deleteEntity(row)">{{ $t('page.delete') }}</li>
                <li class="tsfont-circulation-s" @click="viewData(1, 10, row.name)">{{ $t('page.viewdata') }}</li>
              </ul>
            </div>
          </template>
        </TsTable>
      </template>
    </TsContain>
    <ResourceEditDialog
      v-if="isEditShow"
      :name="currentEntityName"
      :isCopy="isCopy"
      @close="closeEntityDialog"
    ></ResourceEditDialog>
    <TsDialog
      v-bind="dialogConfig"
      @on-close="close"
    >
      <TsTable
        :fixedHeader="false"
        v-bind="viewDataTable"
        @changeCurrent="viewData"
        @changePageSize="viewData(1, ...arguments)"
      ></TsTable>
    </TsDialog>
  </div>
</template>
<script>
export default {
  name: '',
  components: {
    TsTable: () => import('@/resources/components/TsTable/TsTable.vue'),
    ResourceEditDialog: () => import('./resourceentity-edit.vue'),
    InputSearcher: () => import('@/resources/components/InputSearcher/InputSearcher.vue')
  },
  props: {},
  data() {
    return {
      loadingShow: true,
      keyword: '',
      currentEntityId: null,
      isEditShow: false,
      theadList: [
        {
          key: 'name',
          title: this.$t('term.cmdb.view')
        },
        { key: 'label', title: this.$t('page.name') },
        { key: 'moduleName', title: this.$t('term.framework.belongmodule') },
        { key: 'status', title: this.$t('page.status') },
        { key: 'initTime', title: this.$t('page.inittime'), type: 'time' },
        { key: 'description', title: this.$t('page.description') },
        {
          key: 'action'
        }
      ],
      tbodyList: [],
      dialogConfig: {
        type: 'modal',
        maskClose: false,
        isShow: false,
        width: 'large',
        title: '',
        hasFooter: false
      },
      viewDataTable: {},
      viewDataParams: {
        currentPage: 1,
        pageSize: 10,
        name: ''
      },
      isCopy: false
    };
  },
  beforeCreate() {},
  created() {
    this.getResourceEntityList();
  },
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  methods: {
    editEntity(row) {
      this.currentEntityName = row.name;
      this.isEditShow = true;
    },
    getResourceEntityList() {
      this.loadingShow = true;
      this.$api.cmdb.resourceentity.searchResourceEntity().then(res => {
        this.tbodyList = res.Return;
      }).finally(() => {
        this.loadingShow = false;
      });
    },
    viewData(currentPage, pageSize, name) {
      if (currentPage) {
        this.viewDataParams.currentPage = currentPage;
      }
      if (pageSize) {
        this.viewDataParams.pageSize = pageSize;
      }
      if (name) {
        this.dialogConfig.title = name;
        this.viewDataParams.name = name;
      }
      this.$api.cmdb.resourceentity.getResourceEntityViewDataList(this.viewDataParams).then(res => {
        this.viewDataTable = res.Return;
        this.dialogConfig.isShow = true;
      });
    },
    close() {
      this.dialogConfig.isShow = false;
      this.viewDataTable = {};
      this.dialogConfig.title = '';
      this.viewDataParams.name = '';
    },
    closeEntityDialog(needRefresh) {
      this.isEditShow = false;
      this.isCopy = false;
      if (needRefresh) {
        this.getResourceEntityList();
      }
    },
    addData() {
      this.currentEntityName = '';
      this.isEditShow = true; 
    },
    copyEntity(row) {
      this.currentEntityName = row.name;
      this.isCopy = true;
      this.isEditShow = true;
    },
    deleteEntity(row) {
      this.$createDialog({
        title: this.$t('dialog.title.deleteconfirm'),
        content: this.$t('dialog.content.deletetargetconfirm', {'target': row.name}),
        btnType: 'error',
        'on-ok': vnode => {
          this.$api.cmdb.resourceentity.deleteResourceentityData({name: row.name}).then(res => {
            if (res.Status == 'OK') {
              vnode.isShow = false;
              this.$Message.success(this.$t('message.deletesuccess'));
              this.getResourceEntityList();
            }
          });
        }
      });
    }
  },
  filter: {},
  computed: {
    finalTbodyList() {
      if (this.keyword) {
        const k = this.keyword.toLowerCase();
        return this.tbodyList.filter(d => (d.name && d.name.toLowerCase().includes(k)) || (d.label && d.label.toLowerCase().includes(k)) || (d.description && d.description.toLowerCase().includes(k)));
      }
      return this.tbodyList;
    }
  },
  watch: {}
};
</script>
<style lang="less"></style>
