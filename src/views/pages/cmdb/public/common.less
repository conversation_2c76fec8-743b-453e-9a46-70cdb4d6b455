@import (reference) '~@/resources/assets/css/variable.less';

.ci-content {
  .ci-right {
    position: relative;
    width: 100%;
    overflow: auto;
    .right-top {
      height: 64px;
      display: flex;
      align-items: center;
      .icon-block {
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        margin-right: 12px;
        flex-shrink: 0;

        .ci-icon {
          font-size: 20px;
        }
      }
      .title {
        .ci-label {
          font-size: 13px;
        }
      }
    }
    .right-block {
      // margin: 0 16px 16px;
      padding-top: 16px;
    }
  }
}
.ci-left {
  padding: 12px 16px;
  // height: 100%;
  position: relative;
  width: 100%;
  .left-block {
    margin-bottom: 10px;
    .text-type {
      padding-bottom: 9px;
      border-bottom: 1px solid;
      // line-height: 38px;
    }
    .text-ci {
      cursor: pointer;
      height: 32px;
      line-height: 32px;
    }
  }
}
