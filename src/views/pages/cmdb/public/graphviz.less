@import (reference) '~@/resources/assets/css/variable.less';
.theme(@layer-color, @edge-stroke, @node-fill, @node-name, @link-stroke, @anchor-color, @mask-color, @cluster-color,@core-color) {
  @node-removeIcon: @link-stroke;
  .graph .layer text {
    fill: @layer-color;
  }
  .graph .normalnode text {
    fill: @layer-color;
  }
  .graph .corenode text {
    fill: @core-color;
  }
  .graph .selectednode text {
    fill: @cluster-color !important;
    text-decoration: underline;
    font-weight: bold;
  }
  .graph .cluster text {
    fill: @cluster-color;
    font-family: Arial;
    font-size: 12px;
  }
  .graph .cluster polygon {
    fill: @cluster-color;
    fill-opacity: 0.1;
    stroke: @cluster-color;
    stroke-opacity: 0.1;
  }
  .graph .edge path {
    stroke: @edge-stroke;
  }
  .graph .edge polygon {
    stroke: @edge-stroke;
    fill: @edge-stroke;
  }
  .graph .edge text {
    fill: @edge-stroke;
  }

  .graph .abstract_ci {
    image {
      filter: grayscale(100%);
    }
  }

  .graph text {
    user-select: none;
  }

  .graph .virtual_ci {
    image {
      filter: invert(0.4);
    }
  }
  .graph .cientitybg {
    fill: @mask-color;
  }
}

html {
  .theme(@default-text, @default-bg-code, @default-dividing, @default-title, @default-icon, @default-blockbg, @default-background, @default-primary-color,@default-error-color);

  &.theme-dark {
    .theme(@dark-text, @dark-invert-bg, @dark-dividing, @dark-title, @dark-icon, @dark-blockbg, @dark-background, @dark-primary-color,@dark-error-color);
  }
}
