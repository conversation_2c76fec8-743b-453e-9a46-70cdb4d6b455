<template>
  <div>
    <TsContain>
      <template v-slot:navigation>
        <span v-if="$hasBack()" class="tsfont-left text-action" @click="$back()">{{ $getFromPage() }}</span>
      </template>
      <template v-slot:topLeft>{{ title }}</template>
      <template v-slot:topRight>
        <div class="action-group">
          <div class="action-item">
            <Poptip
              v-if="!hasAuth"
              width="400"
              trigger="hover"
              placement="top"
              :content="$t('term.deploy.notversionproductauth')"
            >
              <TsFormSwitch
                v-model="isFreeze"
                :trueValue="1"
                :falseValue="0"
                :disabled="!hasAuth"
              ></TsFormSwitch>
            </Poptip>
            <TsFormSwitch
              v-else
              v-model="isFreeze"
              :showStatus="true"
              :trueValue="1"
              :falseValue="0"
              :trueText="$t('term.deploy.sealplate')"
              :falseText="$t('term.deploy.sealplate')"
              @on-change="isFreeze => switchLockVersion(versionId, isFreeze)"
            ></TsFormSwitch>
          </div>
          <div class="action-item">
            <span class="tsfont-file-single" @click="openProjectDirectoryDialog(versionId)">
              {{ $t('term.deploy.projectdirectory') }}
            </span>
          </div>
          <div class="action-item">
            <span class="tsfont-trash-o" @click="deleteVersion(versionId, versionName, true)">{{ $t('page.delete') }}</span>
          </div>
        </div>
      </template>
      <template v-slot:content>
        <Tabs v-model="tabValue" :animated="false" class="block-tabs">
          <TabPane :label="$t('term.deploy.publishstatus')" name="deployStatus">
            <DeployStatusOverview v-if="tabValue == 'deployStatus'" :envId="envId" :versionId="versionId"></DeployStatusOverview>
          </TabPane>
          <TabPane :label="$t('page.unittest')" name="unitTest">
            <UnitTestOverview v-if="tabValue == 'unitTest'" :versionId="versionId"></UnitTestOverview>
          </TabPane>
          <TabPane :label="$t('page.codescan')" name="codeScan">
            <CodeScanOverview v-if="tabValue == 'codeScan'" :versionId="versionId"></CodeScanOverview>
          </TabPane>
          <TabPane v-if="componentName == 'codeChange'" :label="$t('term.deploy.codechange')" name="codeChange">
            <component
              :is="componentName"
              v-if="tabValue == 'codeChange'"
              :versionId="versionId"
              url="/api/rest/deploy/version/commit/diff/get"
              :readOnly="true"
              :canBinaryFileDownload="false"
              :canExpandContent="false"
            ></component>
          </TabPane>
          <TabPane v-if="isShowCveTab && versionId" :label="$t('term.deploy.cveloophole')" name="cveLoophole">
            <CveLoopholeManage :versionId="versionId" @hideTab="(hideTab) => isShowCveTab = hideTab"></CveLoopholeManage>
          </TabPane>
          <TabPane :label="$t('term.rdm.relativerequest')" name="relatedIssues">
            <RelatedIssuesManage v-if="versionId && tabValue == 'relatedIssues'" :versionId="versionId"></RelatedIssuesManage>
          </TabPane>
        </Tabs>
      </template>
    </TsContain>
    <ProjectDirectoryDialog
      v-if="isShowProjectDirectoryDialog"
      :params="projectDirectoryParams"
      :hasAllAuth="hasAuth"
      @close="isShowProjectDirectoryDialog = false"
    ></ProjectDirectoryDialog>
  </div>
</template>
<script>
import versionCenterMixin from './versionCenterMixin.js';
import ImportComponent from '@/views/components/import-component.js';
export default {
  name: '',
  components: {
    TsFormSwitch: () => import('@/resources/plugins/TsForm/TsFormSwitch'),
    ProjectDirectoryDialog: () => import('./project-directory-dialog'), // 工程目录
    DeployStatusOverview: () => import('./detail/deploy-status-overview'),
    UnitTestOverview: () => import('./detail/unit-test-overview'), // 单元测试
    CodeScanOverview: () => import('./detail/code-scan-overview'), // 代码扫描
    CveLoopholeManage: () => import('./detail/cve-loophole-manage'), // cve漏洞
    RelatedIssuesManage: () => import('./detail/related-issues-manage'), // 关联需求
    ...ImportComponent
  },
  mixins: [versionCenterMixin],
  props: {},
  data() {
    return {
      tabValue: 'deployStatus',
      title: '',
      envId: null,
      versionId: null,
      isFreeze: 0, // 封版
      versionName: '', // 版本名称
      hasAuth: false,
      isShowCodeChange: false, // 是否显示代码变更的tab，无数据时，不显示这个tab
      isShowCveTab: true,
      componentName: ImportComponent.hasOwnProperty('codeChange') ? 'codeChange' : 'div'
    };
  },
  beforeCreate() {},
  created() {
    let query = this.$route.query || {};
    if (query && !this.$utils.isEmptyObj(query)) {
      this.title = query.title;
      query.envId && (this.envId = parseInt(query.envId));
      this.versionId = parseInt(query.versionId);
      this.versionName = query.versionName;
      this.isFreeze = parseInt(query.isFreeze);
      this.hasAuth = Boolean(query.hasAuth);
    }
  },
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  methods: {},
  filter: {},
  computed: {},
  watch: {}
};
</script>
<style lang="less">
</style>
