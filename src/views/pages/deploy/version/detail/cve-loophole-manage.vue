<template>
  <div class="pl-nm pr-nm pt-nm">
    <Loading
      v-if="loadingShow"
      :loadingShow="loadingShow"
      type="fix"
    ></Loading>
    <TsTable
      v-else
      v-bind="tableConfig"
      :theadList="theadList"
      @changeCurrent="changeCurrent"
      @changePageSize="changePageSize"
    >
      <template slot="highestSeverity" slot-scope="{row}">
        <span :style="{color: getColorByHighestSeverity(row.highestSeverity)}">{{ row.highestSeverity }}</span>
      </template>
      <template slot="confidence" slot-scope="{row}">
        <span :style="{color: getColorByConfidence(row.confidence)}">{{ row.confidence }}</span>
      </template>
      <template slot="vulnerabilityIds" slot-scope="{row}">
        <div
          v-for="(item, index) in row.vulnerabilityIds"
          :key="index"
          class="mb-xs"
          :class="item.url ? 'text-href' : ''"
          @click="tovulnerabilityPage(item.url)"
        >{{ item.vulnerabilityId }}</div>
      </template>
      <template slot="packageList" slot-scope="{row}">
        <div
          v-for="(item, index) in row.packageList"
          :key="index"
          class="mb-xs"
          :class="item.url ? 'text-href' : ''"
          @click="tovulnerabilityPage(item.url)"
        >
          {{ item.packageName }}
        </div>
      </template>
    </TsTable>
  </div>
</template>
<script>
export default {
  name: '',
  components: {
    TsTable: () => import('@/resources/components/TsTable/TsTable.vue')
  },
  props: {
    versionId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      loadingShow: true,
      tableConfig: {
        currentPage: 1,
        pageSize: 20,
        tbodyList: []
      },
      theadList: [
        {
          title: 'Dependency',
          key: 'dependency'
        },
        {
          title: 'HighestSeverity',
          key: 'highestSeverity'
        },
        {
          title: 'Confidence',
          key: 'confidence'
        },
        {
          title: 'Vulnerability IDs',
          key: 'vulnerabilityIds'
        },
        {
          title: 'Package',
          key: 'packageList'
        },
        {
          title: 'CVE Count',
          key: 'cveCount'
        },
        {
          title: 'Evidence Count',
          key: 'evidenceCount'
        }
      ]
    };
  },
  beforeCreate() {},
  created() {
    this.searchCevData();
  },
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  methods: {
    changeCurrent(currentPage) {
      this.tableConfig.currentPage = currentPage;
      this.searchCevData();
    },
    changePageSize(pageSize) {
      this.tableConfig.currentPage = 1;
      this.tableConfig.pageSize = pageSize;
      this.searchCevData();
    },
    searchCevData() {
      let params = {
        versionId: this.versionId,
        currentPage: this.tableConfig.currentPage,
        pageSize: this.tableConfig.pageSize
      };
      if (!this.versionId) {
        this.loadingShow = false;
        this.$emit('hideTab', false);
        return false;
      }
      this.loadingShow = true;
      this.$api.deploy.version.getCveloopholeList(params).then(res => {
        if (res.Status == 'OK') {
          Object.assign(this.tableConfig, res.Return);
          this.$emit('hideTab', !this.$utils.isEmpty(this.tableConfig.tbodyList)); // 提供给外部使用，无数据时，不显示整个tab
        }
      }).finally(() => {
        this.loadingShow = false;
      });
    },
    tovulnerabilityPage(url) {
      if (!url) {
        return false;
      }
      window.open(url, '_blank');
    }
  },
  filter: {},
  computed: {
    getColorByHighestSeverity() {
      return (status) => {
        const statusObj = {
          'CRITICAL*': 'red',
          CRITICAL: 'darkred',
          HIGH: '#d9534f',
          MEDIUM: '#ec971f',
          LOW: '#f2cc0c'
        };
        return statusObj[status];
      };
    },
    getColorByConfidence() {
      return (status) => {
        const statusObj = {
          Highest: 'red',
          High: '#d9534f',
          Medium: '#ec971f',
          Low: '#f2cc0c'
        };
        return statusObj[status];
      };
    }
  },
  watch: {}
};
</script>
<style lang="less">
</style>
