<template>
  <TsDialog v-bind="dialogConfig" @on-close="closeDialog" @on-ok="savePrivateAccount">
    <TsForm ref="form" v-model="formValue" :item-list="formConfig">
    </TsForm>
  </TsDialog>
</template>
<script>
export default {
  name: '',
  components: {
    TsForm: () => import('@/resources/plugins/TsForm/TsForm')
  },
  filters: {},
  props: {
    accountId: {
      // 账号id
      type: Number,
      default: null
    },
    resourceId: {
      // 资产id
      type: Number,
      default: null
    },
    accountList: {
      // 账号列表
      type: Array,
      default: function() {
        return [];
      }
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      protocol: '', // 协议
      formValue: {
        id: this.accountId,
        name: '', // 前端拼接：用户名[协议]
        account: '',
        passwordPlain: null,
        protocolId: null,
        tagIdList: []
      },
      dialogConfig: {
        type: 'modal',
        isShow: true,
        title: this.accountId ? this.$t('dialog.title.edittarget', {'target': this.$t('page.privateaccount')}) : this.$t('page.newtarget', {'target': this.$t('page.privateaccount')})
      },
      formConfig: {
        id: {
          type: 'text',
          name: 'id',
          isHidden: true,
          value: this.accountId
        },
        name: {
          type: 'text',
          name: 'name',
          width: '100%',
          label: this.$t('page.name'),
          maxlength: 50,
          isHidden: true
        },
        account: {
          type: 'text',
          name: 'account',
          width: '100%',
          label: this.$t('page.username'),
          maxlength: 50,
          validateList: ['required', {
            name: 'custom',
            trigger: 'change',
            message: this.$t('term.cmdb.repeataccount'),
            validator: (rule, val) => {
              return this.validAccountProtocalUnique(val);
            }
          }],
          isHidden: false
        },
        passwordPlain: {
          type: 'password',
          name: 'passwordPlain',
          width: '100%',
          label: this.$t('page.password')
        },
        protocolId: {
          type: 'select',
          width: '100%',
          label: '协议',
          placeholder: this.$t('form.validate.selectprotocol'),
          dynamicUrl: '/api/rest/resourcecenter/account/protocol/search',
          rootName: 'tbodyList',
          dealDataByUrl: (nodeList) => { return this.dealProtocolIdDataByUrl(nodeList); },
          name: 'protocolId',
          errorMessage: '',
          validateList: ['required', {
            name: 'custom',
            trigger: 'change',
            message: this.$t('term.cmdb.repeataccount'),
            validator: (rule, protocolId) => {
              return this.validAccountProtocalUnique();
            }
          }],
          transfer: true,
          onChangelabel: (label) => {
            this.protocol = label;
          },
          onChange: (val, objectValue) => {
            this.protocol = objectValue ? objectValue.text : '';
            if (!this.validAccountProtocalUnique()) {
              this.$set(this.formConfig['protocolId'], 'errorMessage', this.$t('term.cmdb.repeataccount'));
            } else {
              this.$set(this.formConfig['protocolId'], 'errorMessage', '');
            }
            if (val == '***************') {
              // 如果选择为 tagent 的时候，用户名为非必填
              this.formConfig.account.validateList = [];
              this.formConfig.account.isHidden = true; // 隐藏账号字段
            } else {
              this.formConfig.account.validateList = ['required',
                {
                  name: 'custom',
                  trigger: 'change',
                  message: this.$t('term.cmdb.repeataccount'),
                  validator: (rule, val) => {
                    return this.validAccountProtocalUnique(val);
                  }
                }];
              this.formConfig.account.isHidden = false;
            }
          }
        },
        tagIdList: {
          type: 'select',
          name: 'tagIdList',
          width: '100%',
          label: this.$t('page.tag'),
          transfer: true,
          multiple: true,
          search: true,
          allowCreate: true,
          dynamicUrl: '/api/rest/resourcecenter/tag/list/forselect',
          rootName: 'tbodyList',
          textName: 'name',
          valueName: 'id'
        }
      }
    };
  },
  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {
    this.getAccountInfoById();
  },
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  methods: {
    getProtocalName(protocalPort) {
      let protocolName = protocalPort;
      return protocolName;
    },
    validAccountProtocalUnique(account) {
      // 验证协议和账号唯一
      let isValid = true;
      let accountName = '';
      let accountList = this.$utils.deepClone(this.accountList) || []; // 编辑私有账号时，不验证自身协议和账号唯一
      accountList = accountList.filter((item) => {
        return item.value != this.accountId;
      });
      if (this.protocol && (account || (this.formValue && this.formValue.account))) {
        accountName = account || (this.formValue && this.formValue.account);
        for (let i = 0; i < accountList.length; i++) {
          if (accountList[i] && accountList[i].config && accountList[i].config.account && (accountList[i].config.account == accountName) && (accountList[i].config.protocol == this.protocol)) {
            isValid = false;
          }
        }
      }
      if (isValid) {
        this.$set(this.formConfig['protocolId'], 'errorMessage', '');
      }
      return isValid;
    },
    savePrivateAccount() {
      const form = this.$refs['form'];
      let formValue = this.$refs.form.getFormValue();
      if (!form.valid()) {
        return;
      }
      let data = {
        ...formValue, 
        appSystemId: this.params.appSystemId,
        appModuleId: this.params.appModuleId,
        envId: this.params.envId,
        type: 'private', 
        resourceId: this.resourceId,
        name: `${formValue.account}[${this.protocol}]`
      };
      // 私有账号
      this.$api.deploy.env.saveEnvDbPrivateaccount(data).then(res => {
        if (res && (res.Status == 'OK')) {
          this.handleTipsMessage(res.Return);
        }
      });
    },
    handleTipsMessage(res) {
      // 失败，错误提示
      if (res && res.failureReasonList && res.failureReasonList instanceof Array && res.failureReasonList.length > 0) {
        this.$Notice.error({
          title: this.$t('term.framework.errorinfo'),
          duration: 10,
          render: h => {
            return h('div', [
              h(
                'ul',
                { class: 'pb-md'},
                res.failureReasonList.map(item => {
                  return h('li', {}, item || '');
                })
              ),
              h('div', {}, this.$t('term.cmdb.repeataccount'))
            ]);
          }
        });
      } else {
        this.$Message.success(this.$t('message.savesuccess'));
        this.closeDialog(true);
      }
    },
    getAccountInfoById() {
      if (this.accountId) {
        this.$api.cmdb.accountManage.getAccountById(this.accountId).then(res => {
          this.tableData = res.Return;
          for (let key in this.formConfig) {
            this.$set(this.formValue, [key], this.tableData[key]);
          }
          if (this.tableData.tagList && this.tableData.tagList.length > 0) {
            let idList = [];
            this.tableData.tagList.forEach(v => {
              idList.push(v.id);
            });
            this.$set(this.formValue, 'tagIdList', idList);
          }
        });
      }
    },
    dealProtocolIdDataByUrl(nodeList) {
      // 处理协议id下拉列表
      let dataList = [];
      if (nodeList && nodeList.length > 0) {
        nodeList.forEach((item) => {
          dataList.push({
            text: item.name,
            value: item.id,
            _disabled: item.id == '***************'
          });
        });
      }
      return dataList;
    },
    closeDialog(needRefresh) {
      this.$emit('close', needRefresh);
    }
  },
  computed: {},
  watch: {}
};
</script>
