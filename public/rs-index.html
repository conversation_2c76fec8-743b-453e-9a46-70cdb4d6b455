<!DOCTYPE html>
<html lang="en">
  <head>
    <meta />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta charset="utf-8" />
    <meta http-equiv="Page-Enter" content="revealTrans(duration=2, transition=8)" />
    <meta http-equiv="Page-Exit" content="revealTrans(duration=2, transition=9)" />
    <link rel="icon" href="<%= assetPrefix %>/resource/img/common/tsfavicon.png" />
    <script src="<%= assetPrefix %>/resource/config.js?version=1.0.3"></script>
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }
      #iframe-container {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body class="border-radius">
    <div id="index"></div>
    <script type="text/javascript">
      document.body.ondrop = function (event) {
        //使用拖拽组件draggble，火狐浏览器弹出新窗口的兼容问题
        event.preventDefault();
        event.stopPropagation();
      };
    </script>
  </body>
</html>
